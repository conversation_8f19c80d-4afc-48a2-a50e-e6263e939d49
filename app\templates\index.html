{% extends "base.html" %}

{% block title %}FaceMatchPro - 人脸相似度比对工具{% endblock %}

{% block content %}
<!-- 英雄区域 -->
<section class="hero" style="text-align: center; padding: var(--spacing-2xl) 0;">
    <div style="max-width: 800px; margin: 0 auto;">
        <h1 style="font-size: 3rem; font-weight: 700; color: var(--text-primary); margin-bottom: var(--spacing-lg);">
            🔍 FaceMatchPro
        </h1>
        <p style="font-size: 1.25rem; color: var(--text-secondary); margin-bottom: var(--spacing-xl); line-height: 1.6;">
            基于深度学习的现代化人脸相似度比对工具<br>
            支持多种算法、实时检测、批量处理
        </p>
        <div class="flex justify-center gap-4">
            <a href="{{ url_for('views.upload') }}" class="btn btn-primary btn-lg">
                📁 开始上传
            </a>
            <a href="{{ url_for('views.detection') }}" class="btn btn-outline btn-lg">
                🔍 人脸检测
            </a>
        </div>
    </div>
</section>

<!-- 功能特性 -->
<section style="padding: var(--spacing-2xl) 0;">
    <h2 class="text-center text-2xl font-bold mb-6">核心功能</h2>
    
    <div class="grid grid-cols-3 gap-6">
        <div class="card">
            <div class="card-body text-center">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">📤</div>
                <h3 class="font-semibold mb-2">智能上传</h3>
                <p class="text-secondary">
                    支持拖拽上传、批量处理<br>
                    自动文件验证和格式转换
                </p>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body text-center">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">👤</div>
                <h3 class="font-semibold mb-2">人脸检测</h3>
                <p class="text-secondary">
                    多算法支持：OpenCV、Dlib<br>
                    高精度检测和人脸提取
                </p>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body text-center">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">⚖️</div>
                <h3 class="font-semibold mb-2">相似度比对</h3>
                <p class="text-secondary">
                    FaceNet、ArcFace算法<br>
                    精确的相似度计算
                </p>
            </div>
        </div>
    </div>
</section>

<!-- 技术优势 -->
<section style="padding: var(--spacing-2xl) 0; background: var(--bg-tertiary);">
    <div class="container">
        <h2 class="text-center text-2xl font-bold mb-6">技术优势</h2>
        
        <div class="grid grid-cols-2 gap-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🚀 高性能算法</h3>
                </div>
                <div class="card-body">
                    <ul style="list-style: none; padding: 0;">
                        <li style="padding: var(--spacing-xs) 0; border-bottom: 1px solid var(--border-color);">
                            ✅ OpenCV Haar级联检测器
                        </li>
                        <li style="padding: var(--spacing-xs) 0; border-bottom: 1px solid var(--border-color);">
                            ✅ Dlib HOG + CNN人脸检测
                        </li>
                        <li style="padding: var(--spacing-xs) 0; border-bottom: 1px solid var(--border-color);">
                            ✅ FaceNet深度特征提取
                        </li>
                        <li style="padding: var(--spacing-xs) 0;">
                            ✅ ArcFace高精度识别
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🛠️ 现代化架构</h3>
                </div>
                <div class="card-body">
                    <ul style="list-style: none; padding: 0;">
                        <li style="padding: var(--spacing-xs) 0; border-bottom: 1px solid var(--border-color);">
                            ✅ Flask RESTful API设计
                        </li>
                        <li style="padding: var(--spacing-xs) 0; border-bottom: 1px solid var(--border-color);">
                            ✅ 模块化算法架构
                        </li>
                        <li style="padding: var(--spacing-xs) 0; border-bottom: 1px solid var(--border-color);">
                            ✅ 会话式文件管理
                        </li>
                        <li style="padding: var(--spacing-xs) 0;">
                            ✅ 响应式Web界面
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 使用流程 -->
<section style="padding: var(--spacing-2xl) 0;">
    <h2 class="text-center text-2xl font-bold mb-6">使用流程</h2>
    
    <div class="grid grid-cols-4 gap-4">
        <div class="text-center">
            <div class="card">
                <div class="card-body">
                    <div style="font-size: 2rem; color: var(--primary-color); margin-bottom: var(--spacing-sm);">1️⃣</div>
                    <h4 class="font-semibold mb-2">上传图像</h4>
                    <p class="text-sm text-secondary">拖拽或选择图像文件</p>
                </div>
            </div>
        </div>
        
        <div class="text-center">
            <div class="card">
                <div class="card-body">
                    <div style="font-size: 2rem; color: var(--primary-color); margin-bottom: var(--spacing-sm);">2️⃣</div>
                    <h4 class="font-semibold mb-2">检测人脸</h4>
                    <p class="text-sm text-secondary">自动识别和提取人脸</p>
                </div>
            </div>
        </div>
        
        <div class="text-center">
            <div class="card">
                <div class="card-body">
                    <div style="font-size: 2rem; color: var(--primary-color); margin-bottom: var(--spacing-sm);">3️⃣</div>
                    <h4 class="font-semibold mb-2">选择算法</h4>
                    <p class="text-sm text-secondary">配置比对算法参数</p>
                </div>
            </div>
        </div>
        
        <div class="text-center">
            <div class="card">
                <div class="card-body">
                    <div style="font-size: 2rem; color: var(--primary-color); margin-bottom: var(--spacing-sm);">4️⃣</div>
                    <h4 class="font-semibold mb-2">获取结果</h4>
                    <p class="text-sm text-secondary">查看相似度分析报告</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 统计信息 -->
<section style="padding: var(--spacing-2xl) 0; background: var(--bg-tertiary);">
    <div class="container">
        <h2 class="text-center text-2xl font-bold mb-6">实时统计</h2>
        
        <div class="grid grid-cols-4 gap-4" id="stats-container">
            <div class="card text-center">
                <div class="card-body">
                    <div class="stat-value" id="uploaded-files">0</div>
                    <div class="stat-label">已上传文件</div>
                </div>
            </div>
            
            <div class="card text-center">
                <div class="card-body">
                    <div class="stat-value" id="detected-faces">0</div>
                    <div class="stat-label">检测到人脸</div>
                </div>
            </div>
            
            <div class="card text-center">
                <div class="card-body">
                    <div class="stat-value" id="comparisons-made">0</div>
                    <div class="stat-label">完成比对</div>
                </div>
            </div>
            
            <div class="card text-center">
                <div class="card-body">
                    <div class="stat-value" id="session-time">00:00</div>
                    <div class="stat-label">会话时长</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 快速开始 -->
<section style="padding: var(--spacing-2xl) 0;">
    <div class="text-center">
        <h2 class="text-2xl font-bold mb-4">准备开始？</h2>
        <p class="text-lg text-secondary mb-6">
            立即体验强大的人脸相似度比对功能
        </p>
        <div class="flex justify-center gap-4">
            <a href="{{ url_for('views.upload') }}" class="btn btn-primary btn-lg">
                🚀 立即开始
            </a>
            <a href="{{ url_for('views.about') }}" class="btn btn-outline btn-lg">
                📖 了解更多
            </a>
        </div>
    </div>
</section>
{% endblock %}

{% block scripts %}
<script>
// 加载统计信息
async function loadStats() {
    try {
        // 加载文件统计
        const uploadResponse = await fetch('/api/upload/info');
        if (uploadResponse.ok) {
            const uploadData = await uploadResponse.json();
            if (uploadData.success) {
                document.getElementById('uploaded-files').textContent = uploadData.data.file_count || 0;
            }
        }
        
        // 加载比对统计
        const comparisonResponse = await fetch('/api/comparison/statistics');
        if (comparisonResponse.ok) {
            const comparisonData = await comparisonResponse.json();
            if (comparisonData.success) {
                // 这里可以添加更多统计信息
            }
        }
    } catch (error) {
        console.error('Failed to load stats:', error);
    }
}

// 会话计时器
let sessionStartTime = Date.now();
function updateSessionTime() {
    const elapsed = Math.floor((Date.now() - sessionStartTime) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    document.getElementById('session-time').textContent = 
        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    loadStats();
    
    // 每秒更新会话时间
    setInterval(updateSessionTime, 1000);
    
    // 每30秒刷新统计
    setInterval(loadStats, 30000);
});
</script>
{% endblock %}

{% block page_init %}
// 首页特定初始化
console.log('FaceMatchPro 首页已加载');
{% endblock %}
