#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人脸检测API
"""

from flask import Blueprint, request, jsonify, session
from loguru import logger

from app.services import FaceService, FileService
from app.services.algorithm_manager import algorithm_manager
from app.utils.response_utils import ResponseUtils

detection_bp = Blueprint('detection', __name__, url_prefix='/api/detection')

# 初始化服务
face_service = FaceService()
file_service = FileService()
response_utils = ResponseUtils()

@detection_bp.route('/detect', methods=['POST'])
def detect_faces():
    """
    检测图像中的人脸
    
    Returns:
        JSON响应
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return response_utils.error('缺少请求参数', 400)
        
        # 检查必需参数
        if 'image_path' not in data:
            return response_utils.error('缺少图像路径参数', 400)
        
        image_path = data['image_path']
        detector_name = data.get('detector', None)
        
        # 验证会话
        session_id = session.get('session_id')
        if not session_id:
            return response_utils.error('会话不存在', 400)
        
        # 执行人脸检测
        result = face_service.detect_faces_in_image(image_path, detector_name)
        
        if result['success']:
            logger.info(f"人脸检测成功: {image_path}, 检测到 {result['face_count']} 张人脸")
            return response_utils.success('人脸检测成功', result)
        else:
            return response_utils.error(result['message'], 400, result)
    
    except Exception as e:
        logger.error(f"人脸检测异常: {e}")
        return response_utils.error('人脸检测失败', 500)

@detection_bp.route('/extract', methods=['POST'])
def extract_faces():
    """
    提取人脸区域
    
    Returns:
        JSON响应
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return response_utils.error('缺少请求参数', 400)
        
        # 检查必需参数
        required_params = ['image_path', 'faces']
        for param in required_params:
            if param not in data:
                return response_utils.error(f'缺少参数: {param}', 400)
        
        image_path = data['image_path']
        faces = data['faces']
        
        # 验证会话
        session_id = session.get('session_id')
        if not session_id:
            return response_utils.error('会话不存在', 400)
        
        # 生成输出目录
        from app.utils.session_manager import SessionManager
        session_manager = SessionManager()
        output_dir = session_manager.get_session_path(session_id, 'upload', 'face')
        
        # 提取人脸区域
        face_paths = face_service.extract_face_regions(image_path, faces, str(output_dir))
        
        if face_paths:
            result = {
                'image_path': image_path,
                'extracted_faces': len(face_paths),
                'face_paths': face_paths,
                'output_directory': str(output_dir)
            }
            
            logger.info(f"人脸提取成功: {len(face_paths)} 张人脸")
            return response_utils.success('人脸提取成功', result)
        else:
            return response_utils.error('人脸提取失败', 400)
    
    except Exception as e:
        logger.error(f"人脸提取异常: {e}")
        return response_utils.error('人脸提取失败', 500)

@detection_bp.route('/analyze', methods=['POST'])
def analyze_face():
    """
    分析人脸信息
    
    Returns:
        JSON响应
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return response_utils.error('缺少请求参数', 400)
        
        # 检查必需参数
        if 'image_path' not in data:
            return response_utils.error('缺少图像路径参数', 400)
        
        image_path = data['image_path']
        
        # 验证会话
        session_id = session.get('session_id')
        if not session_id:
            return response_utils.error('会话不存在', 400)
        
        # 执行人脸分析
        result = face_service.get_face_analysis(image_path)
        
        if result['success']:
            logger.info(f"人脸分析成功: {image_path}")
            return response_utils.success('人脸分析成功', result)
        else:
            return response_utils.error(result['message'], 400, result)
    
    except Exception as e:
        logger.error(f"人脸分析异常: {e}")
        return response_utils.error('人脸分析失败', 500)

@detection_bp.route('/detectors', methods=['GET'])
def get_detectors():
    """
    获取可用的人脸检测算法
    
    Returns:
        JSON响应
    """
    try:
        # 获取可用的检测器
        detectors = algorithm_manager.get_available_detectors()
        
        # 获取详细信息
        detector_info = {}
        for detector_name in detectors:
            try:
                info = algorithm_manager.get_detector_info(detector_name)
                detector_info[detector_name] = info
            except Exception as e:
                logger.warning(f"获取检测器信息失败 {detector_name}: {e}")
        
        result = {
            'available_detectors': detectors,
            'detector_info': detector_info,
            'default_detector': detectors[0] if detectors else None
        }
        
        return response_utils.success('获取检测器列表成功', result)
    
    except Exception as e:
        logger.error(f"获取检测器列表异常: {e}")
        return response_utils.error('获取检测器列表失败', 500)

@detection_bp.route('/detector/config', methods=['POST'])
def configure_detector():
    """
    配置人脸检测算法参数
    
    Returns:
        JSON响应
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return response_utils.error('缺少请求参数', 400)
        
        # 检查必需参数
        required_params = ['detector_name', 'threshold']
        for param in required_params:
            if param not in data:
                return response_utils.error(f'缺少参数: {param}', 400)
        
        detector_name = data['detector_name']
        threshold = data['threshold']
        
        # 验证阈值范围
        if not (0.0 <= threshold <= 1.0):
            return response_utils.error('阈值必须在0.0到1.0之间', 400)
        
        # 设置检测器阈值
        algorithm_manager.set_detector_threshold(detector_name, threshold)
        
        result = {
            'detector_name': detector_name,
            'threshold': threshold,
            'message': f'检测器 {detector_name} 阈值已设置为 {threshold}'
        }
        
        logger.info(f"检测器配置成功: {detector_name}, 阈值: {threshold}")
        return response_utils.success('检测器配置成功', result)
    
    except ValueError as e:
        return response_utils.error(str(e), 400)
    except Exception as e:
        logger.error(f"检测器配置异常: {e}")
        return response_utils.error('检测器配置失败', 500)

@detection_bp.route('/batch', methods=['POST'])
def batch_detect():
    """
    批量人脸检测
    
    Returns:
        JSON响应
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return response_utils.error('缺少请求参数', 400)
        
        # 检查必需参数
        if 'image_paths' not in data:
            return response_utils.error('缺少图像路径列表参数', 400)
        
        image_paths = data['image_paths']
        detector_name = data.get('detector', None)
        
        if not isinstance(image_paths, list) or len(image_paths) == 0:
            return response_utils.error('图像路径列表不能为空', 400)
        
        # 验证会话
        session_id = session.get('session_id')
        if not session_id:
            return response_utils.error('会话不存在', 400)
        
        # 批量检测
        results = []
        success_count = 0
        
        for image_path in image_paths:
            try:
                result = face_service.detect_faces_in_image(image_path, detector_name)
                if result['success']:
                    success_count += 1
                results.append(result)
            except Exception as e:
                logger.error(f"批量检测失败 {image_path}: {e}")
                results.append({
                    'success': False,
                    'image_path': image_path,
                    'error': str(e)
                })
        
        # 统计结果
        batch_result = {
            'total_images': len(image_paths),
            'success_count': success_count,
            'failed_count': len(image_paths) - success_count,
            'detector': detector_name or algorithm_manager.get_available_detectors()[0],
            'results': results
        }
        
        logger.info(f"批量检测完成: {success_count}/{len(image_paths)}")
        return response_utils.success(f'批量检测完成，成功 {success_count}/{len(image_paths)} 个', batch_result)
    
    except Exception as e:
        logger.error(f"批量检测异常: {e}")
        return response_utils.error('批量检测失败', 500)

@detection_bp.route('/statistics', methods=['GET'])
def get_detection_statistics():
    """
    获取检测统计信息
    
    Returns:
        JSON响应
    """
    try:
        # 验证会话
        session_id = session.get('session_id')
        if not session_id:
            return response_utils.error('会话不存在', 400)
        
        # 获取会话文件
        files_result = file_service.list_session_files(session_id)
        
        if not files_result['success']:
            return response_utils.error('获取文件列表失败', 400)
        
        # 统计信息
        original_files = [f for f in files_result['files'] if f['file_type'] == 'original']
        face_files = [f for f in files_result['files'] if f['file_type'] == 'face']
        
        statistics = {
            'session_id': session_id,
            'total_uploaded_images': len(original_files),
            'total_extracted_faces': len(face_files),
            'available_detectors': algorithm_manager.get_available_detectors(),
            'detector_count': len(algorithm_manager.get_available_detectors())
        }
        
        return response_utils.success('获取统计信息成功', statistics)
    
    except Exception as e:
        logger.error(f"获取统计信息异常: {e}")
        return response_utils.error('获取统计信息失败', 500)
