#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Dlib的人脸检测算法
"""

import cv2
import numpy as np
from typing import List, Tuple
from loguru import logger

from ..base.face_detector_base import FaceDetectorBase, FaceBox

try:
    import dlib
    DLIB_AVAILABLE = True
except ImportError:
    DLIB_AVAILABLE = False
    logger.warning("Dlib未安装，相关功能将不可用")

class DlibDetector(FaceDetectorBase):
    """基于Dlib HOG的人脸检测"""
    
    def __init__(self, confidence_threshold: float = 0.5, upsample_times: int = 1):
        """
        初始化Dlib人脸检测器
        
        Args:
            confidence_threshold: 置信度阈值
            upsample_times: 上采样次数
        """
        super().__init__("Dlib_HOG", confidence_threshold)
        self.upsample_times = upsample_times
        self.detector = None
        self.predictor = None
    
    def initialize(self) -> bool:
        """初始化Dlib人脸检测器"""
        if not DLIB_AVAILABLE:
            logger.error("Dlib未安装，无法初始化")
            return False
        
        try:
            # 初始化HOG人脸检测器
            self.detector = dlib.get_frontal_face_detector()
            
            # 可选：初始化关键点检测器
            try:
                # 需要下载shape_predictor_68_face_landmarks.dat文件
                predictor_path = "models/shape_predictor_68_face_landmarks.dat"
                self.predictor = dlib.shape_predictor(predictor_path)
                logger.info("Dlib关键点检测器加载成功")
            except:
                logger.warning("Dlib关键点检测器加载失败，将不提供关键点信息")
                self.predictor = None
            
            logger.info("Dlib人脸检测器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"Dlib人脸检测器初始化失败: {e}")
            return False
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """图像预处理"""
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        return gray
    
    def detect_faces(self, image: np.ndarray) -> List[FaceBox]:
        """检测人脸"""
        try:
            # 检测人脸
            faces = self.detector(image, self.upsample_times)
            
            face_boxes = []
            for face in faces:
                x = face.left()
                y = face.top()
                w = face.width()
                h = face.height()
                
                # Dlib HOG检测器没有置信度，使用固定值
                confidence = 0.9
                
                # 提取关键点（如果可用）
                landmarks = None
                if self.predictor is not None:
                    try:
                        shape = self.predictor(image, face)
                        landmarks = [(shape.part(i).x, shape.part(i).y) for i in range(68)]
                    except:
                        landmarks = None
                
                face_box = FaceBox(
                    x=x,
                    y=y,
                    width=w,
                    height=h,
                    confidence=confidence,
                    landmarks=landmarks
                )
                face_boxes.append(face_box)
            
            logger.debug(f"Dlib检测到 {len(face_boxes)} 张人脸")
            return face_boxes
            
        except Exception as e:
            logger.error(f"Dlib人脸检测失败: {e}")
            return []
    
    def get_algorithm_info(self):
        """获取算法信息"""
        info = super().get_algorithm_info()
        info.update({
            'upsample_times': self.upsample_times,
            'has_landmarks': self.predictor is not None,
            'description': 'Dlib HOG人脸检测器'
        })
        return info

class DlibCNNDetector(FaceDetectorBase):
    """基于Dlib CNN的人脸检测"""
    
    def __init__(self, confidence_threshold: float = 0.5, model_path: str = None):
        """
        初始化Dlib CNN人脸检测器
        
        Args:
            confidence_threshold: 置信度阈值
            model_path: CNN模型文件路径
        """
        super().__init__("Dlib_CNN", confidence_threshold)
        self.model_path = model_path or "models/mmod_human_face_detector.dat"
        self.detector = None
    
    def initialize(self) -> bool:
        """初始化CNN模型"""
        if not DLIB_AVAILABLE:
            logger.error("Dlib未安装，无法初始化")
            return False
        
        try:
            # 加载CNN人脸检测器
            self.detector = dlib.cnn_face_detection_model_v1(self.model_path)
            
            logger.info("Dlib CNN人脸检测器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"Dlib CNN人脸检测器初始化失败: {e}")
            logger.error("请确保已下载mmod_human_face_detector.dat模型文件")
            return False
    
    def detect_faces(self, image: np.ndarray) -> List[FaceBox]:
        """检测人脸"""
        try:
            # 检测人脸
            detections = self.detector(image)
            
            face_boxes = []
            for detection in detections:
                confidence = detection.confidence
                
                if confidence >= self.confidence_threshold:
                    rect = detection.rect
                    x = rect.left()
                    y = rect.top()
                    w = rect.width()
                    h = rect.height()
                    
                    face_box = FaceBox(
                        x=x,
                        y=y,
                        width=w,
                        height=h,
                        confidence=float(confidence)
                    )
                    face_boxes.append(face_box)
            
            logger.debug(f"Dlib CNN检测到 {len(face_boxes)} 张人脸")
            return face_boxes
            
        except Exception as e:
            logger.error(f"Dlib CNN人脸检测失败: {e}")
            return []
    
    def get_algorithm_info(self):
        """获取算法信息"""
        info = super().get_algorithm_info()
        info.update({
            'model_path': self.model_path,
            'description': 'Dlib CNN人脸检测器'
        })
        return info
