#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置模块
"""

import os
from pathlib import Path
from datetime import timedelta

# 项目根目录
BASE_DIR = Path(__file__).parent.parent

class BaseConfig:
    """基础配置类"""
    
    # Flask基础配置
    SECRET_KEY = os.getenv('SECRET_KEY', 'facematchpro-secret-key-2024')
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER = BASE_DIR / 'uploads'
    TEMP_FOLDER = BASE_DIR / 'temp'
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    SESSION_COOKIE_SECURE = False
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # 算法配置
    FACE_DETECTION_CONFIDENCE = 0.5
    FACE_SIMILARITY_THRESHOLD = 0.6
    MAX_FACES_PER_IMAGE = 10
    
    # 文件清理配置
    TEMP_FILE_LIFETIME = timedelta(hours=2)
    AUTO_CLEANUP_INTERVAL = timedelta(minutes=30)
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = BASE_DIR / 'logs' / 'app.log'
    LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5
    
    # CORS配置
    CORS_ORIGINS = ['http://localhost:3000', 'http://127.0.0.1:3000']
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        # 创建必要目录
        for folder in [BaseConfig.UPLOAD_FOLDER, BaseConfig.TEMP_FOLDER]:
            folder.mkdir(exist_ok=True)
        
        # 创建日志目录
        BaseConfig.LOG_FILE.parent.mkdir(exist_ok=True)

class DevelopmentConfig(BaseConfig):
    """开发环境配置"""
    
    DEBUG = True
    TESTING = False
    
    # 开发环境特定配置
    LOG_LEVEL = 'DEBUG'
    
    # 算法配置（开发环境可以降低要求）
    FACE_DETECTION_CONFIDENCE = 0.3
    FACE_SIMILARITY_THRESHOLD = 0.5

class ProductionConfig(BaseConfig):
    """生产环境配置"""
    
    DEBUG = False
    TESTING = False
    
    # 生产环境安全配置
    SESSION_COOKIE_SECURE = True
    
    # 性能配置
    FACE_DETECTION_CONFIDENCE = 0.7
    FACE_SIMILARITY_THRESHOLD = 0.7
    
    # 日志配置
    LOG_LEVEL = 'WARNING'

class TestingConfig(BaseConfig):
    """测试环境配置"""
    
    DEBUG = True
    TESTING = True
    
    # 测试环境配置
    WTF_CSRF_ENABLED = False
    
    # 测试文件夹
    UPLOAD_FOLDER = BASE_DIR / 'tests' / 'uploads'
    TEMP_FOLDER = BASE_DIR / 'tests' / 'temp'
    
    # 算法配置（测试环境使用较低阈值）
    FACE_DETECTION_CONFIDENCE = 0.1
    FACE_SIMILARITY_THRESHOLD = 0.3

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
