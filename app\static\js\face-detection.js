// 人脸检测组件

class FaceDetection {
    constructor(app) {
        this.app = app;
        this.currentImage = null;
        this.detectedFaces = [];
        this.selectedDetector = null;
        
        this.init();
    }
    
    init() {
        this.loadDetectors();
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // 图像选择
        const imageSelect = document.querySelector('#image-select');
        if (imageSelect) {
            imageSelect.addEventListener('change', (e) => {
                this.selectImage(e.target.value);
            });
        }
        
        // 检测器选择
        const detectorSelect = document.querySelector('#detector-select');
        if (detectorSelect) {
            detectorSelect.addEventListener('change', (e) => {
                this.selectedDetector = e.target.value;
            });
        }
        
        // 检测按钮
        const detectBtn = document.querySelector('#detect-btn');
        if (detectBtn) {
            detectBtn.addEventListener('click', () => {
                this.detectFaces();
            });
        }
        
        // 提取人脸按钮
        const extractBtn = document.querySelector('#extract-btn');
        if (extractBtn) {
            extractBtn.addEventListener('click', () => {
                this.extractFaces();
            });
        }
        
        // 批量检测按钮
        const batchDetectBtn = document.querySelector('#batch-detect-btn');
        if (batchDetectBtn) {
            batchDetectBtn.addEventListener('click', () => {
                this.batchDetect();
            });
        }
    }
    
    async loadDetectors() {
        try {
            const response = await this.app.apiRequest('/detection/detectors');
            if (response.success) {
                this.renderDetectorOptions(response.data);
            }
        } catch (error) {
            console.error('Failed to load detectors:', error);
            this.app.showNotification('加载检测器失败', 'error');
        }
    }
    
    renderDetectorOptions(data) {
        const detectorSelect = document.querySelector('#detector-select');
        if (!detectorSelect) return;
        
        detectorSelect.innerHTML = data.available_detectors.map(detector => 
            `<option value="${detector}">${detector}</option>`
        ).join('');
        
        if (data.default_detector) {
            detectorSelect.value = data.default_detector;
            this.selectedDetector = data.default_detector;
        }
        
        // 显示检测器信息
        this.renderDetectorInfo(data.detector_info);
    }
    
    renderDetectorInfo(detectorInfo) {
        const infoContainer = document.querySelector('#detector-info');
        if (!infoContainer) return;
        
        infoContainer.innerHTML = Object.entries(detectorInfo).map(([name, info]) => `
            <div class="algorithm-option">
                <div class="algorithm-title">${name}</div>
                <div class="algorithm-description">${info.description || '暂无描述'}</div>
                <div class="algorithm-metrics">
                    <div class="metric-item">
                        <span class="metric-label">置信度阈值:</span>
                        <span class="metric-value">${info.confidence_threshold}</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">状态:</span>
                        <span class="metric-value">${info.initialized ? '已初始化' : '未初始化'}</span>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    async loadImages() {
        try {
            const response = await this.app.apiRequest('/upload/list?type=original');
            if (response.success) {
                this.renderImageOptions(response.data.files);
            }
        } catch (error) {
            console.error('Failed to load images:', error);
        }
    }
    
    renderImageOptions(files) {
        const imageSelect = document.querySelector('#image-select');
        if (!imageSelect) return;
        
        imageSelect.innerHTML = '<option value="">选择图像...</option>' + 
            files.map(file => 
                `<option value="${file.file_path}">${file.filename}</option>`
            ).join('');
    }
    
    selectImage(imagePath) {
        if (!imagePath) return;
        
        this.currentImage = imagePath;
        this.displayImage(imagePath);
        this.clearDetectionResults();
    }
    
    displayImage(imagePath) {
        const imageContainer = document.querySelector('#image-container');
        if (!imageContainer) return;
        
        // 构建图像URL
        const imageUrl = imagePath.replace(/\\/g, '/').replace(/^.*\/upload\//, '/static/uploads/');
        
        imageContainer.innerHTML = `
            <div class="image-container">
                <img id="detection-image" src="${imageUrl}" class="image-display" alt="检测图像">
                <div class="image-overlay" id="face-overlay"></div>
            </div>
        `;
    }
    
    async detectFaces() {
        if (!this.currentImage) {
            this.app.showNotification('请先选择图像', 'warning');
            return;
        }
        
        const detectBtn = document.querySelector('#detect-btn');
        const originalText = detectBtn.textContent;
        
        try {
            detectBtn.disabled = true;
            detectBtn.innerHTML = '<span class="loading-spinner"></span> 检测中...';
            
            const response = await this.app.apiRequest('/detection/detect', {
                method: 'POST',
                body: JSON.stringify({
                    image_path: this.currentImage,
                    detector: this.selectedDetector
                })
            });
            
            if (response.success) {
                this.detectedFaces = response.data.faces;
                this.renderDetectionResults(response.data);
                this.drawFaceBoxes();
                this.app.showNotification(`检测完成，发现 ${this.detectedFaces.length} 张人脸`, 'success');
            }
        } catch (error) {
            this.app.showNotification('人脸检测失败: ' + error.message, 'error');
        } finally {
            detectBtn.disabled = false;
            detectBtn.textContent = originalText;
        }
    }
    
    renderDetectionResults(data) {
        const resultsContainer = document.querySelector('#detection-results');
        if (!resultsContainer) return;
        
        resultsContainer.innerHTML = `
            <div class="result-summary">
                <div class="result-stats">
                    <div class="stat-item">
                        <span class="stat-value">${data.face_count}</span>
                        <span class="stat-label">检测到的人脸</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">${data.detector}</span>
                        <span class="stat-label">使用算法</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">${data.image_size.width}×${data.image_size.height}</span>
                        <span class="stat-label">图像尺寸</span>
                    </div>
                </div>
            </div>
            
            <div class="face-list">
                ${data.faces.map((face, index) => `
                    <div class="face-item" data-face-index="${index}">
                        <div class="face-id">人脸 ${index + 1}</div>
                        <div class="face-info">
                            <div>位置: (${face.x}, ${face.y})</div>
                            <div>大小: ${face.width}×${face.height}</div>
                            <div>置信度: ${(face.confidence * 100).toFixed(1)}%</div>
                            <div>面积: ${face.area} 像素</div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
        
        // 添加人脸项点击事件
        const faceItems = resultsContainer.querySelectorAll('.face-item');
        faceItems.forEach((item, index) => {
            item.addEventListener('click', () => {
                this.selectFace(index);
            });
        });
    }
    
    drawFaceBoxes() {
        const overlay = document.querySelector('#face-overlay');
        const image = document.querySelector('#detection-image');
        
        if (!overlay || !image || !this.detectedFaces.length) return;
        
        // 等待图像加载完成
        if (!image.complete) {
            image.onload = () => this.drawFaceBoxes();
            return;
        }
        
        overlay.innerHTML = '';
        
        const imageRect = image.getBoundingClientRect();
        const scaleX = imageRect.width / image.naturalWidth;
        const scaleY = imageRect.height / image.naturalHeight;
        
        this.detectedFaces.forEach((face, index) => {
            const faceBox = document.createElement('div');
            faceBox.className = 'face-box';
            faceBox.dataset.faceIndex = index;
            
            const x = face.x * scaleX;
            const y = face.y * scaleY;
            const width = face.width * scaleX;
            const height = face.height * scaleY;
            
            faceBox.style.cssText = `
                left: ${x}px;
                top: ${y}px;
                width: ${width}px;
                height: ${height}px;
            `;
            
            faceBox.innerHTML = `
                <div class="face-label">
                    人脸 ${index + 1}
                    <div class="face-confidence">${(face.confidence * 100).toFixed(1)}%</div>
                </div>
            `;
            
            faceBox.addEventListener('click', () => {
                this.selectFace(index);
            });
            
            overlay.appendChild(faceBox);
        });
    }
    
    selectFace(index) {
        // 移除之前的选中状态
        document.querySelectorAll('.face-box.selected, .face-item.selected').forEach(el => {
            el.classList.remove('selected');
        });
        
        // 添加新的选中状态
        const faceBox = document.querySelector(`[data-face-index="${index}"]`);
        if (faceBox) {
            faceBox.classList.add('selected');
        }
        
        const faceItem = document.querySelector(`.face-item[data-face-index="${index}"]`);
        if (faceItem) {
            faceItem.classList.add('selected');
            faceItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    }
    
    async extractFaces() {
        if (!this.detectedFaces.length) {
            this.app.showNotification('请先检测人脸', 'warning');
            return;
        }
        
        try {
            this.app.showLoading('#detection-results', '提取人脸中...');
            
            const response = await this.app.apiRequest('/detection/extract', {
                method: 'POST',
                body: JSON.stringify({
                    image_path: this.currentImage,
                    faces: this.detectedFaces
                })
            });
            
            if (response.success) {
                this.app.showNotification(`成功提取 ${response.data.extracted_faces} 张人脸`, 'success');
                
                // 刷新文件列表
                if (this.app.loadUploadedFiles) {
                    this.app.loadUploadedFiles();
                }
            }
        } catch (error) {
            this.app.showNotification('人脸提取失败: ' + error.message, 'error');
        } finally {
            this.app.hideLoading('#detection-results');
        }
    }
    
    async batchDetect() {
        try {
            const response = await this.app.apiRequest('/upload/list?type=original');
            if (!response.success || !response.data.files.length) {
                this.app.showNotification('没有可检测的图像', 'warning');
                return;
            }
            
            const imagePaths = response.data.files.map(file => file.file_path);
            
            this.app.showLoading('#detection-results', '批量检测中...');
            
            const batchResponse = await this.app.apiRequest('/detection/batch', {
                method: 'POST',
                body: JSON.stringify({
                    image_paths: imagePaths,
                    detector: this.selectedDetector
                })
            });
            
            if (batchResponse.success) {
                this.renderBatchResults(batchResponse.data);
                this.app.showNotification(
                    `批量检测完成: ${batchResponse.data.success_count}/${batchResponse.data.total_images}`, 
                    'success'
                );
            }
        } catch (error) {
            this.app.showNotification('批量检测失败: ' + error.message, 'error');
        } finally {
            this.app.hideLoading('#detection-results');
        }
    }
    
    renderBatchResults(data) {
        const resultsContainer = document.querySelector('#detection-results');
        if (!resultsContainer) return;
        
        resultsContainer.innerHTML = `
            <div class="result-summary">
                <h3>批量检测结果</h3>
                <div class="result-stats">
                    <div class="stat-item">
                        <span class="stat-value">${data.total_images}</span>
                        <span class="stat-label">总图像数</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">${data.success_count}</span>
                        <span class="stat-label">成功检测</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">${data.results.reduce((sum, r) => sum + (r.face_count || 0), 0)}</span>
                        <span class="stat-label">总人脸数</span>
                    </div>
                </div>
            </div>
            
            <div class="batch-results">
                ${data.results.map(result => `
                    <div class="batch-result-item ${result.success ? 'success' : 'error'}">
                        <div class="result-image">${result.image_path.split('/').pop()}</div>
                        <div class="result-info">
                            ${result.success ? 
                                `检测到 ${result.face_count} 张人脸` : 
                                `错误: ${result.error || '检测失败'}`
                            }
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    clearDetectionResults() {
        const resultsContainer = document.querySelector('#detection-results');
        if (resultsContainer) {
            resultsContainer.innerHTML = '';
        }
        
        const overlay = document.querySelector('#face-overlay');
        if (overlay) {
            overlay.innerHTML = '';
        }
        
        this.detectedFaces = [];
    }
}

// 扩展主应用
if (window.app) {
    window.app.initDetectionComponent = function() {
        this.faceDetection = new FaceDetection(this);
        
        // 加载图像列表
        this.faceDetection.loadImages();
    };
}
