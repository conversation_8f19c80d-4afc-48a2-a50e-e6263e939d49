#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传API
"""

from flask import Blueprint, request, jsonify, session
from werkzeug.datastructures import FileStorage
from loguru import logger

from app.services import FileService
from app.utils.session_manager import SessionManager
from app.utils.response_utils import ResponseUtils

upload_bp = Blueprint('upload', __name__, url_prefix='/api/upload')

# 初始化服务
file_service = FileService()
session_manager = SessionManager()
response_utils = ResponseUtils()

@upload_bp.route('/image', methods=['POST'])
def upload_image():
    """
    上传图像文件
    
    Returns:
        JSON响应
    """
    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return response_utils.error('没有选择文件', 400)
        
        file = request.files['file']
        
        # 检查文件是否为空
        if file.filename == '':
            return response_utils.error('文件名为空', 400)
        
        # 获取或创建会话ID
        session_id = session.get('session_id')
        if not session_id:
            session_id = session_manager.create_session()
            session['session_id'] = session_id
        
        # 保存文件
        result = file_service.save_uploaded_file(file, session_id, 'original')
        
        if result['success']:
            # 返回成功响应
            response_data = {
                'file_id': result['filename'],
                'file_path': result['file_path'],
                'session_id': session_id,
                'file_info': result['file_info']
            }
            
            logger.info(f"文件上传成功: {result['filename']}")
            return response_utils.success('文件上传成功', response_data)
        else:
            return response_utils.error(result['message'], 400)
    
    except Exception as e:
        logger.error(f"文件上传异常: {e}")
        return response_utils.error('文件上传失败', 500)

@upload_bp.route('/batch', methods=['POST'])
def upload_batch():
    """
    批量上传图像文件
    
    Returns:
        JSON响应
    """
    try:
        # 检查是否有文件
        if 'files' not in request.files:
            return response_utils.error('没有选择文件', 400)
        
        files = request.files.getlist('files')
        
        if not files or len(files) == 0:
            return response_utils.error('文件列表为空', 400)
        
        # 获取或创建会话ID
        session_id = session.get('session_id')
        if not session_id:
            session_id = session_manager.create_session()
            session['session_id'] = session_id
        
        # 批量保存文件
        results = []
        success_count = 0
        
        for file in files:
            if file.filename == '':
                continue
            
            result = file_service.save_uploaded_file(file, session_id, 'original')
            
            if result['success']:
                success_count += 1
                results.append({
                    'filename': result['filename'],
                    'file_path': result['file_path'],
                    'file_info': result['file_info'],
                    'success': True
                })
            else:
                results.append({
                    'filename': file.filename,
                    'error': result['message'],
                    'success': False
                })
        
        # 返回批量上传结果
        response_data = {
            'session_id': session_id,
            'total_files': len(files),
            'success_count': success_count,
            'failed_count': len(files) - success_count,
            'results': results
        }
        
        logger.info(f"批量上传完成: {success_count}/{len(files)}")
        return response_utils.success(f'批量上传完成，成功 {success_count}/{len(files)} 个文件', response_data)
    
    except Exception as e:
        logger.error(f"批量上传异常: {e}")
        return response_utils.error('批量上传失败', 500)

@upload_bp.route('/validate', methods=['POST'])
def validate_file():
    """
    验证文件是否符合要求
    
    Returns:
        JSON响应
    """
    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return response_utils.error('没有选择文件', 400)
        
        file = request.files['file']
        
        if file.filename == '':
            return response_utils.error('文件名为空', 400)
        
        # 验证文件
        validation_result = {
            'filename': file.filename,
            'is_valid': True,
            'errors': [],
            'warnings': []
        }
        
        # 检查文件扩展名
        if not file_service.file_utils.is_allowed_file(file.filename):
            validation_result['is_valid'] = False
            validation_result['errors'].append('不支持的文件格式')
        
        # 检查文件大小
        file.seek(0, 2)  # 移动到文件末尾
        file_size = file.tell()
        file.seek(0)  # 重置文件指针
        
        if not file_service.file_utils.validate_file_size(file_size):
            validation_result['is_valid'] = False
            validation_result['errors'].append('文件大小超出限制')
        
        # 检查是否为危险文件
        if file_service.file_utils.is_dangerous_file(file.filename):
            validation_result['is_valid'] = False
            validation_result['errors'].append('危险文件类型')
        
        # 添加文件信息
        validation_result['file_size'] = file_size
        validation_result['file_size_human'] = file_service.file_utils.format_file_size(file_size)
        validation_result['mime_type'] = file_service.file_utils.get_mime_type(file.filename)
        
        if validation_result['is_valid']:
            return response_utils.success('文件验证通过', validation_result)
        else:
            return response_utils.error('文件验证失败', 400, validation_result)
    
    except Exception as e:
        logger.error(f"文件验证异常: {e}")
        return response_utils.error('文件验证失败', 500)

@upload_bp.route('/list', methods=['GET'])
def list_files():
    """
    列出当前会话的上传文件
    
    Returns:
        JSON响应
    """
    try:
        # 获取会话ID
        session_id = session.get('session_id')
        if not session_id:
            return response_utils.error('会话不存在', 400)
        
        # 获取文件类型过滤
        file_type = request.args.get('type', None)
        
        # 列出文件
        result = file_service.list_session_files(session_id, file_type)
        
        if result['success']:
            return response_utils.success('获取文件列表成功', result)
        else:
            return response_utils.error(result['message'], 400)
    
    except Exception as e:
        logger.error(f"获取文件列表异常: {e}")
        return response_utils.error('获取文件列表失败', 500)

@upload_bp.route('/delete', methods=['DELETE'])
def delete_file():
    """
    删除上传的文件
    
    Returns:
        JSON响应
    """
    try:
        # 获取文件路径
        data = request.get_json()
        if not data or 'file_path' not in data:
            return response_utils.error('缺少文件路径参数', 400)
        
        file_path = data['file_path']
        
        # 验证文件路径安全性
        session_id = session.get('session_id')
        if not session_id:
            return response_utils.error('会话不存在', 400)
        
        # 删除文件
        result = file_service.delete_file(file_path)
        
        if result['success']:
            return response_utils.success('文件删除成功', result)
        else:
            return response_utils.error(result['message'], 400)
    
    except Exception as e:
        logger.error(f"删除文件异常: {e}")
        return response_utils.error('删除文件失败', 500)

@upload_bp.route('/cleanup', methods=['POST'])
def cleanup_session():
    """
    清理当前会话的所有文件
    
    Returns:
        JSON响应
    """
    try:
        # 获取会话ID
        session_id = session.get('session_id')
        if not session_id:
            return response_utils.error('会话不存在', 400)
        
        # 清理会话文件
        result = file_service.cleanup_session_files(session_id)
        
        if result['success']:
            # 清除会话
            session.pop('session_id', None)
            return response_utils.success('会话清理成功', result)
        else:
            return response_utils.error(result['message'], 400)
    
    except Exception as e:
        logger.error(f"清理会话异常: {e}")
        return response_utils.error('清理会话失败', 500)

@upload_bp.route('/info', methods=['GET'])
def get_upload_info():
    """
    获取上传配置信息
    
    Returns:
        JSON响应
    """
    try:
        from flask import current_app
        
        info = {
            'max_file_size': current_app.config.get('MAX_CONTENT_LENGTH', 16 * 1024 * 1024),
            'max_file_size_human': file_service.file_utils.format_file_size(
                current_app.config.get('MAX_CONTENT_LENGTH', 16 * 1024 * 1024)
            ),
            'allowed_extensions': list(file_service.file_utils.IMAGE_EXTENSIONS),
            'session_id': session.get('session_id'),
            'storage_info': file_service.get_storage_info()
        }
        
        return response_utils.success('获取上传信息成功', info)
    
    except Exception as e:
        logger.error(f"获取上传信息异常: {e}")
        return response_utils.error('获取上传信息失败', 500)
