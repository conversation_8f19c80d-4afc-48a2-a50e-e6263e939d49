{% extends "base.html" %}

{% block title %}关于 - FaceMatchPro{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- 页面标题 -->
    <div class="text-center mb-8">
        <h1 class="text-3xl font-bold mb-4">📖 关于 FaceMatchPro</h1>
        <p class="text-lg text-secondary">基于深度学习的现代化人脸相似度比对工具</p>
    </div>
    
    <!-- 项目介绍 -->
    <div class="card mb-6">
        <div class="card-header">
            <h2 class="card-title">项目简介</h2>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-2 gap-6">
                <div>
                    <h3 class="font-semibold mb-3">🎯 项目目标</h3>
                    <p class="text-secondary mb-4">
                        FaceMatchPro 是一个基于 Python 和 Flask 的现代化人脸相似度比对工具，
                        旨在提供高精度、易使用的人脸识别和比对服务。
                    </p>
                    
                    <h3 class="font-semibold mb-3">✨ 核心特性</h3>
                    <ul class="list-disc list-inside text-secondary space-y-1">
                        <li>多种人脸检测算法支持</li>
                        <li>先进的相似度计算方法</li>
                        <li>直观的Web界面操作</li>
                        <li>批量处理和分析功能</li>
                        <li>实时结果展示</li>
                        <li>模块化架构设计</li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-semibold mb-3">🏗️ 技术架构</h3>
                    <div class="space-y-3">
                        <div class="tech-item">
                            <strong>后端框架:</strong> Flask + Python 3.8+
                        </div>
                        <div class="tech-item">
                            <strong>计算机视觉:</strong> OpenCV, Dlib
                        </div>
                        <div class="tech-item">
                            <strong>深度学习:</strong> FaceNet, ArcFace
                        </div>
                        <div class="tech-item">
                            <strong>前端技术:</strong> HTML5, CSS3, JavaScript ES6+
                        </div>
                        <div class="tech-item">
                            <strong>数据处理:</strong> NumPy, PIL
                        </div>
                        <div class="tech-item">
                            <strong>日志系统:</strong> Loguru
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 算法介绍 -->
    <div class="grid grid-cols-2 gap-6 mb-6">
        <!-- 人脸检测算法 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">👤 人脸检测算法</h2>
            </div>
            <div class="card-body">
                <div class="algorithm-list space-y-4">
                    <div class="algorithm-item">
                        <h4 class="font-semibold text-primary">OpenCV Haar级联</h4>
                        <p class="text-sm text-secondary">
                            基于Haar特征的经典人脸检测算法，速度快，适合实时应用。
                        </p>
                    </div>
                    
                    <div class="algorithm-item">
                        <h4 class="font-semibold text-primary">OpenCV DNN</h4>
                        <p class="text-sm text-secondary">
                            基于深度神经网络的检测方法，精度高，对光照和角度变化鲁棒。
                        </p>
                    </div>
                    
                    <div class="algorithm-item">
                        <h4 class="font-semibold text-primary">Dlib HOG</h4>
                        <p class="text-sm text-secondary">
                            基于方向梯度直方图的检测器，在正面人脸检测上表现优秀。
                        </p>
                    </div>
                    
                    <div class="algorithm-item">
                        <h4 class="font-semibold text-primary">Dlib CNN</h4>
                        <p class="text-sm text-secondary">
                            基于卷积神经网络的高精度检测器，适合复杂场景。
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 相似度计算算法 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">⚖️ 相似度计算算法</h2>
            </div>
            <div class="card-body">
                <div class="algorithm-list space-y-4">
                    <div class="algorithm-item">
                        <h4 class="font-semibold text-success">余弦相似度</h4>
                        <p class="text-sm text-secondary">
                            计算特征向量间的夹角余弦值，对向量长度不敏感。
                        </p>
                    </div>
                    
                    <div class="algorithm-item">
                        <h4 class="font-semibold text-success">欧几里得距离</h4>
                        <p class="text-sm text-secondary">
                            计算特征向量间的直线距离，简单直观的相似度度量。
                        </p>
                    </div>
                    
                    <div class="algorithm-item">
                        <h4 class="font-semibold text-success">FaceNet</h4>
                        <p class="text-sm text-secondary">
                            Google开发的深度学习模型，生成高质量的人脸特征向量。
                        </p>
                    </div>
                    
                    <div class="algorithm-item">
                        <h4 class="font-semibold text-success">ArcFace</h4>
                        <p class="text-sm text-secondary">
                            基于角度边际的损失函数，在人脸识别任务上表现卓越。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统信息 -->
    <div class="card mb-6">
        <div class="card-header">
            <h2 class="card-title">🔧 系统信息</h2>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-3 gap-6">
                <div class="info-section">
                    <h3 class="font-semibold mb-3">版本信息</h3>
                    <div class="info-list space-y-2">
                        <div class="info-item">
                            <span class="info-label">应用版本:</span>
                            <span class="info-value">v1.0.0</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">发布日期:</span>
                            <span class="info-value">2024-01-01</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">构建版本:</span>
                            <span class="info-value">{{ app_version or '1.0.0' }}</span>
                        </div>
                    </div>
                </div>
                
                <div class="info-section">
                    <h3 class="font-semibold mb-3">运行环境</h3>
                    <div class="info-list space-y-2">
                        <div class="info-item">
                            <span class="info-label">Python版本:</span>
                            <span class="info-value" id="python-version">检测中...</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Flask版本:</span>
                            <span class="info-value" id="flask-version">检测中...</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">OpenCV版本:</span>
                            <span class="info-value" id="opencv-version">检测中...</span>
                        </div>
                    </div>
                </div>
                
                <div class="info-section">
                    <h3 class="font-semibold mb-3">系统状态</h3>
                    <div class="info-list space-y-2">
                        <div class="info-item">
                            <span class="info-label">服务状态:</span>
                            <span class="info-value status-online">🟢 在线</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">会话ID:</span>
                            <span class="info-value">{{ current_session or '未创建' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">运行模式:</span>
                            <span class="info-value">{{ 'development' if is_development else 'production' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 使用指南 -->
    <div class="card mb-6">
        <div class="card-header">
            <h2 class="card-title">📚 使用指南</h2>
        </div>
        <div class="card-body">
            <div class="guide-steps">
                <div class="step-item">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h4 class="step-title">上传图像文件</h4>
                        <p class="step-description">
                            在"文件上传"页面，通过拖拽或点击选择的方式上传包含人脸的图像文件。
                            支持 JPG、PNG、BMP、TIFF 等常见格式。
                        </p>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h4 class="step-title">检测人脸</h4>
                        <p class="step-description">
                            在"人脸检测"页面，选择检测算法并对上传的图像进行人脸检测，
                            系统会自动标记和提取检测到的人脸区域。
                        </p>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h4 class="step-title">比对分析</h4>
                        <p class="step-description">
                            在"人脸比对"页面，选择两张人脸和相似度计算算法，
                            系统会计算并显示详细的相似度分析结果。
                        </p>
                    </div>
                </div>
                
                <div class="step-item">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h4 class="step-title">结果分析</h4>
                        <p class="step-description">
                            查看相似度分数、置信度区间、算法性能对比等详细信息，
                            支持批量处理和历史记录查询。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 开发团队 -->
    <div class="card mb-6">
        <div class="card-header">
            <h2 class="card-title">👥 开发团队</h2>
        </div>
        <div class="card-body">
            <div class="text-center">
                <div class="team-info">
                    <h3 class="font-semibold mb-2">FaceMatchPro 开发团队</h3>
                    <p class="text-secondary mb-4">
                        致力于提供高质量的人脸识别和比对解决方案
                    </p>
                    
                    <div class="contact-info">
                        <div class="contact-item">
                            <strong>项目地址:</strong> 
                            <a href="#" class="text-primary">https://github.com/facematchpro/facematchpro</a>
                        </div>
                        <div class="contact-item">
                            <strong>技术支持:</strong> 
                            <a href="mailto:<EMAIL>" class="text-primary"><EMAIL></a>
                        </div>
                        <div class="contact-item">
                            <strong>文档中心:</strong> 
                            <a href="#" class="text-primary">https://docs.facematchpro.com</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 许可证信息 -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">📄 许可证信息</h2>
        </div>
        <div class="card-body">
            <div class="license-info">
                <p class="text-secondary mb-4">
                    FaceMatchPro 基于 MIT 许可证开源发布，您可以自由使用、修改和分发本软件。
                </p>
                
                <div class="license-details">
                    <h4 class="font-semibold mb-2">第三方组件许可证:</h4>
                    <ul class="list-disc list-inside text-secondary space-y-1">
                        <li>Flask - BSD-3-Clause License</li>
                        <li>OpenCV - Apache License 2.0</li>
                        <li>Dlib - Boost Software License</li>
                        <li>NumPy - BSD License</li>
                        <li>Pillow - PIL Software License</li>
                        <li>Loguru - MIT License</li>
                    </ul>
                </div>
                
                <div class="copyright mt-4">
                    <p class="text-sm text-muted">
                        © 2024 FaceMatchPro Team. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 加载系统信息
async function loadSystemInfo() {
    try {
        const response = await fetch('/version');
        if (response.ok) {
            const data = await response.json();
            // 这里可以更新版本信息显示
        }
        
        // 加载环境信息
        const statusResponse = await fetch('/status');
        if (statusResponse.ok) {
            const statusData = await statusResponse.json();
            // 更新系统状态信息
        }
        
    } catch (error) {
        console.error('Failed to load system info:', error);
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    loadSystemInfo();
    
    // 模拟版本信息（实际应该从API获取）
    document.getElementById('python-version').textContent = 'Python 3.8+';
    document.getElementById('flask-version').textContent = 'Flask 2.0+';
    document.getElementById('opencv-version').textContent = 'OpenCV 4.5+';
});
</script>

<style>
.step-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.step-number {
    flex-shrink: 0;
    width: 2rem;
    height: 2rem;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 1rem;
}

.step-content {
    flex: 1;
}

.step-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.step-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

.algorithm-item {
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background: var(--bg-secondary);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-label {
    color: var(--text-secondary);
}

.info-value {
    font-weight: 500;
    color: var(--text-primary);
}

.status-online {
    color: var(--success-color);
}

.tech-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.tech-item:last-child {
    border-bottom: none;
}

.contact-item {
    margin: 0.5rem 0;
}

.license-details {
    background: var(--bg-tertiary);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
}
</style>
{% endblock %}

{% block page_init %}
// 关于页面特定初始化
console.log('关于页面已加载');
{% endblock %}
