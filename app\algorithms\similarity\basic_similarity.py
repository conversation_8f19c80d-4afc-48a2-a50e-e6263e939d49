#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础相似度计算算法
"""

import cv2
import numpy as np
from typing import Dict, Any
from loguru import logger

from ..base.similarity_calculator_base import SimilarityCalculatorBase, SimilarityResult

class CosineSimilarityCalculator(SimilarityCalculatorBase):
    """基于余弦相似度的人脸比对"""
    
    def __init__(self, similarity_threshold: float = 0.6, feature_method: str = 'histogram'):
        """
        初始化余弦相似度计算器
        
        Args:
            similarity_threshold: 相似度阈值
            feature_method: 特征提取方法 ('histogram', 'lbp', 'hog')
        """
        super().__init__("Cosine_Similarity", similarity_threshold)
        self.feature_method = feature_method
    
    def initialize(self) -> bool:
        """初始化算法"""
        try:
            logger.info(f"余弦相似度计算器初始化成功，特征方法: {self.feature_method}")
            return True
        except Exception as e:
            logger.error(f"余弦相似度计算器初始化失败: {e}")
            return False
    
    def preprocess_face(self, face_image: np.ndarray) -> np.ndarray:
        """人脸图像预处理"""
        # 调整大小
        face_resized = cv2.resize(face_image, (128, 128))
        
        # 转换为灰度图
        if len(face_resized.shape) == 3:
            face_gray = cv2.cvtColor(face_resized, cv2.COLOR_BGR2GRAY)
        else:
            face_gray = face_resized
        
        # 直方图均衡化
        face_equalized = cv2.equalizeHist(face_gray)
        
        return face_equalized
    
    def extract_features(self, face_image: np.ndarray) -> np.ndarray:
        """提取人脸特征"""
        try:
            if self.feature_method == 'histogram':
                return self._extract_histogram_features(face_image)
            elif self.feature_method == 'lbp':
                return self._extract_lbp_features(face_image)
            elif self.feature_method == 'hog':
                return self._extract_hog_features(face_image)
            else:
                raise ValueError(f"不支持的特征提取方法: {self.feature_method}")
                
        except Exception as e:
            logger.error(f"特征提取失败: {e}")
            return np.array([])
    
    def _extract_histogram_features(self, face_image: np.ndarray) -> np.ndarray:
        """提取直方图特征"""
        # 计算灰度直方图
        hist = cv2.calcHist([face_image], [0], None, [256], [0, 256])
        
        # 归一化
        hist = hist.flatten()
        hist = hist / (np.sum(hist) + 1e-7)
        
        return hist
    
    def _extract_lbp_features(self, face_image: np.ndarray) -> np.ndarray:
        """提取LBP特征"""
        try:
            from skimage.feature import local_binary_pattern
            
            # 计算LBP
            radius = 3
            n_points = 8 * radius
            lbp = local_binary_pattern(face_image, n_points, radius, method='uniform')
            
            # 计算LBP直方图
            hist, _ = np.histogram(lbp.ravel(), bins=n_points + 2, range=(0, n_points + 2))
            
            # 归一化
            hist = hist.astype(float)
            hist = hist / (np.sum(hist) + 1e-7)
            
            return hist
            
        except ImportError:
            logger.warning("scikit-image未安装，使用直方图特征替代LBP")
            return self._extract_histogram_features(face_image)
    
    def _extract_hog_features(self, face_image: np.ndarray) -> np.ndarray:
        """提取HOG特征"""
        try:
            from skimage.feature import hog
            
            # 计算HOG特征
            features = hog(
                face_image,
                orientations=9,
                pixels_per_cell=(8, 8),
                cells_per_block=(2, 2),
                block_norm='L2-Hys',
                feature_vector=True
            )
            
            return features
            
        except ImportError:
            logger.warning("scikit-image未安装，使用直方图特征替代HOG")
            return self._extract_histogram_features(face_image)
    
    def calculate_similarity(self, features1: np.ndarray, features2: np.ndarray) -> SimilarityResult:
        """计算余弦相似度"""
        try:
            # 计算余弦相似度
            similarity = self.cosine_similarity(features1, features2)
            
            # 计算欧几里得距离
            distance = self.euclidean_distance(features1, features2)
            
            # 计算置信度（基于特征向量的模长）
            confidence = min(np.linalg.norm(features1), np.linalg.norm(features2))
            confidence = min(confidence / 100.0, 1.0)  # 归一化到[0,1]
            
            result = SimilarityResult(
                similarity=float(similarity),
                distance=float(distance),
                algorithm=self.name,
                confidence=float(confidence),
                metadata={
                    'feature_method': self.feature_method,
                    'feature_dim': len(features1)
                }
            )
            
            return result
            
        except Exception as e:
            logger.error(f"相似度计算失败: {e}")
            return SimilarityResult(
                similarity=0.0,
                distance=float('inf'),
                algorithm=self.name,
                confidence=0.0
            )

class EuclideanSimilarityCalculator(SimilarityCalculatorBase):
    """基于欧几里得距离的人脸比对"""
    
    def __init__(self, similarity_threshold: float = 0.6, max_distance: float = 100.0):
        """
        初始化欧几里得相似度计算器
        
        Args:
            similarity_threshold: 相似度阈值
            max_distance: 最大距离（用于归一化）
        """
        super().__init__("Euclidean_Similarity", similarity_threshold)
        self.max_distance = max_distance
    
    def initialize(self) -> bool:
        """初始化算法"""
        try:
            logger.info("欧几里得相似度计算器初始化成功")
            return True
        except Exception as e:
            logger.error(f"欧几里得相似度计算器初始化失败: {e}")
            return False
    
    def preprocess_face(self, face_image: np.ndarray) -> np.ndarray:
        """人脸图像预处理"""
        # 调整大小
        face_resized = cv2.resize(face_image, (64, 64))
        
        # 转换为灰度图
        if len(face_resized.shape) == 3:
            face_gray = cv2.cvtColor(face_resized, cv2.COLOR_BGR2GRAY)
        else:
            face_gray = face_resized
        
        # 归一化到[0,1]
        face_normalized = face_gray.astype(np.float32) / 255.0
        
        return face_normalized
    
    def extract_features(self, face_image: np.ndarray) -> np.ndarray:
        """提取人脸特征（直接使用像素值）"""
        try:
            # 展平为一维向量
            features = face_image.flatten()
            
            # L2归一化
            norm = np.linalg.norm(features)
            if norm > 0:
                features = features / norm
            
            return features
            
        except Exception as e:
            logger.error(f"特征提取失败: {e}")
            return np.array([])
    
    def calculate_similarity(self, features1: np.ndarray, features2: np.ndarray) -> SimilarityResult:
        """计算基于欧几里得距离的相似度"""
        try:
            # 计算欧几里得距离
            distance = self.euclidean_distance(features1, features2)
            
            # 将距离转换为相似度 (距离越小，相似度越高)
            similarity = max(0.0, 1.0 - distance / self.max_distance)
            
            # 计算置信度
            confidence = 1.0 - min(distance / self.max_distance, 1.0)
            
            result = SimilarityResult(
                similarity=float(similarity),
                distance=float(distance),
                algorithm=self.name,
                confidence=float(confidence),
                metadata={
                    'max_distance': self.max_distance,
                    'feature_dim': len(features1)
                }
            )
            
            return result
            
        except Exception as e:
            logger.error(f"相似度计算失败: {e}")
            return SimilarityResult(
                similarity=0.0,
                distance=float('inf'),
                algorithm=self.name,
                confidence=0.0
            )
