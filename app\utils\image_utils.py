#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像工具类
"""

import cv2
import numpy as np
from pathlib import Path
from typing import Tuple, Optional, List, Dict, Any
from loguru import logger

class ImageUtils:
    """图像处理工具类"""
    
    @staticmethod
    def load_image(image_path: str) -> Optional[np.ndarray]:
        """
        加载图像
        
        Args:
            image_path: 图像路径
            
        Returns:
            Optional[np.ndarray]: 图像数组，失败返回None
        """
        try:
            image = cv2.imread(str(image_path))
            if image is None:
                logger.error(f"无法加载图像: {image_path}")
                return None
            return image
        except Exception as e:
            logger.error(f"加载图像失败: {e}")
            return None
    
    @staticmethod
    def save_image(image: np.ndarray, output_path: str, quality: int = 95) -> bool:
        """
        保存图像
        
        Args:
            image: 图像数组
            output_path: 输出路径
            quality: JPEG质量 (1-100)
            
        Returns:
            bool: 是否成功
        """
        try:
            # 创建输出目录
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 设置保存参数
            ext = Path(output_path).suffix.lower()
            if ext in ['.jpg', '.jpeg']:
                params = [cv2.IMWRITE_JPEG_QUALITY, quality]
            elif ext == '.png':
                params = [cv2.IMWRITE_PNG_COMPRESSION, 9]
            else:
                params = []
            
            success = cv2.imwrite(str(output_path), image, params)
            if not success:
                logger.error(f"保存图像失败: {output_path}")
            return success
            
        except Exception as e:
            logger.error(f"保存图像异常: {e}")
            return False
    
    @staticmethod
    def resize_image(image: np.ndarray, target_size: Tuple[int, int], 
                    keep_aspect_ratio: bool = True) -> np.ndarray:
        """
        调整图像大小
        
        Args:
            image: 输入图像
            target_size: 目标大小 (width, height)
            keep_aspect_ratio: 是否保持宽高比
            
        Returns:
            np.ndarray: 调整后的图像
        """
        try:
            if not keep_aspect_ratio:
                return cv2.resize(image, target_size)
            
            h, w = image.shape[:2]
            target_w, target_h = target_size
            
            # 计算缩放比例
            scale = min(target_w / w, target_h / h)
            new_w = int(w * scale)
            new_h = int(h * scale)
            
            # 调整大小
            resized = cv2.resize(image, (new_w, new_h))
            
            # 如果需要，添加填充
            if new_w != target_w or new_h != target_h:
                # 创建目标大小的黑色图像
                result = np.zeros((target_h, target_w, image.shape[2]), dtype=image.dtype)
                
                # 计算居中位置
                y_offset = (target_h - new_h) // 2
                x_offset = (target_w - new_w) // 2
                
                # 将调整后的图像放置在中心
                result[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized
                return result
            
            return resized
            
        except Exception as e:
            logger.error(f"调整图像大小失败: {e}")
            return image
    
    @staticmethod
    def crop_image(image: np.ndarray, x: int, y: int, width: int, height: int) -> np.ndarray:
        """
        裁剪图像
        
        Args:
            image: 输入图像
            x: 左上角x坐标
            y: 左上角y坐标
            width: 宽度
            height: 高度
            
        Returns:
            np.ndarray: 裁剪后的图像
        """
        try:
            h, w = image.shape[:2]
            
            # 确保坐标在有效范围内
            x = max(0, min(x, w))
            y = max(0, min(y, h))
            x2 = max(x, min(x + width, w))
            y2 = max(y, min(y + height, h))
            
            return image[y:y2, x:x2]
            
        except Exception as e:
            logger.error(f"裁剪图像失败: {e}")
            return image
    
    @staticmethod
    def rotate_image(image: np.ndarray, angle: float, center: Optional[Tuple[int, int]] = None) -> np.ndarray:
        """
        旋转图像
        
        Args:
            image: 输入图像
            angle: 旋转角度（度）
            center: 旋转中心，None表示图像中心
            
        Returns:
            np.ndarray: 旋转后的图像
        """
        try:
            h, w = image.shape[:2]
            
            if center is None:
                center = (w // 2, h // 2)
            
            # 获取旋转矩阵
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
            
            # 执行旋转
            rotated = cv2.warpAffine(image, rotation_matrix, (w, h))
            
            return rotated
            
        except Exception as e:
            logger.error(f"旋转图像失败: {e}")
            return image
    
    @staticmethod
    def enhance_image(image: np.ndarray, brightness: float = 0, contrast: float = 1.0) -> np.ndarray:
        """
        增强图像（调整亮度和对比度）
        
        Args:
            image: 输入图像
            brightness: 亮度调整 (-100 到 100)
            contrast: 对比度调整 (0.5 到 3.0)
            
        Returns:
            np.ndarray: 增强后的图像
        """
        try:
            # 调整亮度和对比度
            enhanced = cv2.convertScaleAbs(image, alpha=contrast, beta=brightness)
            return enhanced
            
        except Exception as e:
            logger.error(f"图像增强失败: {e}")
            return image
    
    @staticmethod
    def convert_to_grayscale(image: np.ndarray) -> np.ndarray:
        """
        转换为灰度图
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 灰度图像
        """
        try:
            if len(image.shape) == 3:
                return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            return image
            
        except Exception as e:
            logger.error(f"转换灰度图失败: {e}")
            return image
    
    @staticmethod
    def apply_gaussian_blur(image: np.ndarray, kernel_size: int = 5, sigma: float = 0) -> np.ndarray:
        """
        应用高斯模糊
        
        Args:
            image: 输入图像
            kernel_size: 核大小（奇数）
            sigma: 标准差，0表示自动计算
            
        Returns:
            np.ndarray: 模糊后的图像
        """
        try:
            # 确保kernel_size是奇数
            if kernel_size % 2 == 0:
                kernel_size += 1
            
            blurred = cv2.GaussianBlur(image, (kernel_size, kernel_size), sigma)
            return blurred
            
        except Exception as e:
            logger.error(f"高斯模糊失败: {e}")
            return image
    
    @staticmethod
    def detect_edges(image: np.ndarray, low_threshold: int = 50, high_threshold: int = 150) -> np.ndarray:
        """
        边缘检测
        
        Args:
            image: 输入图像
            low_threshold: 低阈值
            high_threshold: 高阈值
            
        Returns:
            np.ndarray: 边缘图像
        """
        try:
            # 转换为灰度图
            gray = ImageUtils.convert_to_grayscale(image)
            
            # Canny边缘检测
            edges = cv2.Canny(gray, low_threshold, high_threshold)
            
            return edges
            
        except Exception as e:
            logger.error(f"边缘检测失败: {e}")
            return image
    
    @staticmethod
    def get_image_info(image: np.ndarray) -> Dict[str, Any]:
        """
        获取图像信息
        
        Args:
            image: 输入图像
            
        Returns:
            Dict: 图像信息
        """
        try:
            info = {
                'height': image.shape[0],
                'width': image.shape[1],
                'channels': image.shape[2] if len(image.shape) > 2 else 1,
                'dtype': str(image.dtype),
                'size': image.size,
                'shape': image.shape
            }
            
            # 计算图像统计信息
            if len(image.shape) == 3:
                info['mean'] = [float(np.mean(image[:, :, i])) for i in range(image.shape[2])]
                info['std'] = [float(np.std(image[:, :, i])) for i in range(image.shape[2])]
            else:
                info['mean'] = float(np.mean(image))
                info['std'] = float(np.std(image))
            
            return info
            
        except Exception as e:
            logger.error(f"获取图像信息失败: {e}")
            return {}
    
    @staticmethod
    def create_thumbnail(image: np.ndarray, max_size: int = 200) -> np.ndarray:
        """
        创建缩略图
        
        Args:
            image: 输入图像
            max_size: 最大尺寸
            
        Returns:
            np.ndarray: 缩略图
        """
        try:
            h, w = image.shape[:2]
            
            # 计算缩放比例
            scale = min(max_size / w, max_size / h)
            
            if scale >= 1:
                return image  # 不需要缩放
            
            new_w = int(w * scale)
            new_h = int(h * scale)
            
            thumbnail = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
            return thumbnail
            
        except Exception as e:
            logger.error(f"创建缩略图失败: {e}")
            return image
    
    @staticmethod
    def draw_rectangle(image: np.ndarray, x: int, y: int, width: int, height: int,
                      color: Tuple[int, int, int] = (0, 255, 0), thickness: int = 2) -> np.ndarray:
        """
        在图像上绘制矩形
        
        Args:
            image: 输入图像
            x: 左上角x坐标
            y: 左上角y坐标
            width: 宽度
            height: 高度
            color: 颜色 (B, G, R)
            thickness: 线条粗细
            
        Returns:
            np.ndarray: 绘制后的图像
        """
        try:
            result = image.copy()
            cv2.rectangle(result, (x, y), (x + width, y + height), color, thickness)
            return result
            
        except Exception as e:
            logger.error(f"绘制矩形失败: {e}")
            return image
    
    @staticmethod
    def add_text(image: np.ndarray, text: str, position: Tuple[int, int],
                font_scale: float = 1.0, color: Tuple[int, int, int] = (255, 255, 255),
                thickness: int = 2) -> np.ndarray:
        """
        在图像上添加文本
        
        Args:
            image: 输入图像
            text: 文本内容
            position: 文本位置 (x, y)
            font_scale: 字体大小
            color: 文本颜色 (B, G, R)
            thickness: 线条粗细
            
        Returns:
            np.ndarray: 添加文本后的图像
        """
        try:
            result = image.copy()
            cv2.putText(result, text, position, cv2.FONT_HERSHEY_SIMPLEX,
                       font_scale, color, thickness)
            return result
            
        except Exception as e:
            logger.error(f"添加文本失败: {e}")
            return image
