#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FaceMatchPro 应用启动文件
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app import create_app
from config import DevelopmentConfig, ProductionConfig, TestingConfig

def get_config():
    """根据环境变量获取配置类"""
    env = os.getenv('FLASK_ENV', 'development').lower()
    
    config_map = {
        'development': DevelopmentConfig,
        'production': ProductionConfig,
        'testing': TestingConfig
    }
    
    return config_map.get(env, DevelopmentConfig)

def create_directories():
    """创建必要的目录"""
    directories = [
        'uploads',
        'temp',
        'logs'
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(exist_ok=True)
        print(f"✓ 目录已创建: {dir_path}")

def main():
    """主函数"""
    print("🚀 启动 FaceMatchPro 人脸相似度比对工具...")
    
    # 创建必要目录
    create_directories()
    
    # 获取配置
    config_class = get_config()
    print(f"📋 使用配置: {config_class.__name__}")
    
    # 创建应用实例
    app = create_app(config_class)
    
    # 获取运行参数
    host = os.getenv('FLASK_HOST', '127.0.0.1')
    port = int(os.getenv('FLASK_PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
    
    print(f"🌐 服务地址: http://{host}:{port}")
    print(f"🔧 调试模式: {'开启' if debug else '关闭'}")
    print("=" * 50)
    
    # 启动应用
    try:
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
