#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主要视图控制器
"""

from flask import render_template, session, request, jsonify
from loguru import logger

from . import views_bp
from app.utils.session_utils import ensure_session


@views_bp.route('/')
def index():
    """
    首页
    """
    ensure_session()
    return render_template('index.html')


@views_bp.route('/upload')
def upload():
    """
    文件上传页面
    """
    ensure_session()
    return render_template('upload.html')


@views_bp.route('/detection')
def detection():
    """
    人脸检测页面
    """
    ensure_session()
    return render_template('detection.html')


@views_bp.route('/comparison')
def comparison():
    """
    人脸比对页面
    """
    ensure_session()
    return render_template('comparison.html')


@views_bp.route('/about')
def about():
    """
    关于页面
    """
    return render_template('about.html')


@views_bp.route('/help')
def help():
    """
    帮助页面
    """
    return render_template('help.html')


@views_bp.route('/api-docs')
def api_docs():
    """
    API文档页面
    """
    return render_template('api_docs.html')


@views_bp.route('/settings')
def settings():
    """
    设置页面
    """
    ensure_session()
    return render_template('settings.html')


@views_bp.route('/history')
def history():
    """
    历史记录页面
    """
    ensure_session()
    return render_template('history.html')


@views_bp.route('/batch')
def batch():
    """
    批量处理页面
    """
    ensure_session()
    return render_template('batch.html')


@views_bp.route('/analytics')
def analytics():
    """
    分析统计页面
    """
    ensure_session()
    return render_template('analytics.html')


# 错误处理页面
@views_bp.errorhandler(404)
def not_found(error):
    """
    404错误页面
    """
    return render_template('errors/404.html'), 404


@views_bp.errorhandler(500)
def internal_error(error):
    """
    500错误页面
    """
    logger.error(f"Internal server error: {error}")
    return render_template('errors/500.html'), 500


@views_bp.errorhandler(403)
def forbidden(error):
    """
    403错误页面
    """
    return render_template('errors/403.html'), 403


# 辅助路由
@views_bp.route('/health')
def health_check():
    """
    健康检查
    """
    return jsonify({
        'status': 'healthy',
        'message': 'FaceMatchPro is running',
        'session_id': session.get('session_id')
    })


@views_bp.route('/version')
def version():
    """
    版本信息
    """
    return jsonify({
        'name': 'FaceMatchPro',
        'version': '1.0.0',
        'description': '人脸相似度比对工具',
        'author': 'FaceMatchPro Team'
    })


@views_bp.route('/status')
def status():
    """
    系统状态
    """
    try:
        from app.services.algorithm_manager import algorithm_manager
        
        # 获取算法状态
        detectors = algorithm_manager.get_available_detectors()
        calculators = algorithm_manager.get_available_calculators()
        
        status_info = {
            'system': 'online',
            'session_id': session.get('session_id'),
            'algorithms': {
                'detectors': {
                    'available': detectors,
                    'count': len(detectors)
                },
                'calculators': {
                    'available': calculators,
                    'count': len(calculators)
                }
            },
            'features': {
                'upload': True,
                'detection': len(detectors) > 0,
                'comparison': len(calculators) > 0,
                'batch_processing': True
            }
        }
        
        return jsonify(status_info)
        
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        return jsonify({
            'system': 'error',
            'message': str(e)
        }), 500


# 开发辅助路由
@views_bp.route('/debug')
def debug():
    """
    调试信息页面（仅开发环境）
    """
    if not request.environ.get('FLASK_ENV') == 'development':
        return render_template('errors/404.html'), 404
    
    debug_info = {
        'session': dict(session),
        'request_headers': dict(request.headers),
        'request_args': dict(request.args),
        'environment': {
            'FLASK_ENV': request.environ.get('FLASK_ENV'),
            'FLASK_DEBUG': request.environ.get('FLASK_DEBUG'),
        }
    }
    
    return render_template('debug.html', debug_info=debug_info)


@views_bp.route('/test')
def test():
    """
    测试页面（仅开发环境）
    """
    if not request.environ.get('FLASK_ENV') == 'development':
        return render_template('errors/404.html'), 404
    
    return render_template('test.html')


# 上下文处理器
@views_bp.context_processor
def inject_global_vars():
    """
    注入全局模板变量
    """
    return {
        'app_name': 'FaceMatchPro',
        'app_version': '1.0.0',
        'current_session': session.get('session_id'),
        'is_development': request.environ.get('FLASK_ENV') == 'development'
    }
