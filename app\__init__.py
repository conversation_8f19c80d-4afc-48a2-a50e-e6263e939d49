#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FaceMatchPro Flask应用工厂
"""

import os
import logging
from pathlib import Path
from flask import Flask, request, jsonify
from flask_cors import CORS
from loguru import logger

def create_app(config_object=None):
    """
    Flask应用工厂函数
    
    Args:
        config_object: 配置对象类
        
    Returns:
        Flask: 配置好的Flask应用实例
    """
    
    # 创建Flask应用实例
    app = Flask(__name__)
    
    # 加载配置
    if config_object is None:
        from config import DevelopmentConfig
        config_object = DevelopmentConfig
    
    app.config.from_object(config_object)
    
    # 初始化配置
    config_object.init_app(app)
    
    # 配置CORS
    CORS(app, origins=app.config.get('CORS_ORIGINS', ['*']))
    
    # 配置日志
    setup_logging(app)
    
    # 注册蓝图
    register_blueprints(app)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # 注册请求钩子
    register_request_hooks(app)
    
    # 初始化扩展
    init_extensions(app)
    
    logger.info(f"Flask应用已创建，配置: {config_object.__name__}")
    
    return app

def setup_logging(app):
    """配置日志系统"""
    
    # 移除默认的loguru处理器
    logger.remove()
    
    # 控制台日志
    logger.add(
        sink=lambda msg: print(msg, end=''),
        level=app.config.get('LOG_LEVEL', 'INFO'),
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True
    )
    
    # 文件日志
    log_file = app.config.get('LOG_FILE')
    if log_file:
        logger.add(
            sink=str(log_file),
            level=app.config.get('LOG_LEVEL', 'INFO'),
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation=app.config.get('LOG_MAX_SIZE', '10 MB'),
            retention=app.config.get('LOG_BACKUP_COUNT', 5),
            compression="zip"
        )

def register_blueprints(app):
    """注册蓝图"""
    
    # 导入蓝图
    from app.views import views_bp
    from app.api import api_bp

    # 注册蓝图
    app.register_blueprint(views_bp)
    app.register_blueprint(api_bp, url_prefix='/api')
    
    logger.info("蓝图注册完成")

def register_error_handlers(app):
    """注册错误处理器"""
    
    @app.errorhandler(404)
    def not_found_error(error):
        if request.path.startswith('/api/'):
            return jsonify({'error': '接口不存在', 'code': 404}), 404
        return "页面未找到", 404
    
    @app.errorhandler(500)
    def internal_error(error):
        logger.error(f"内部服务器错误: {error}")
        if request.path.startswith('/api/'):
            return jsonify({'error': '内部服务器错误', 'code': 500}), 500
        return "内部服务器错误", 500
    
    @app.errorhandler(413)
    def file_too_large(error):
        if request.path.startswith('/api/'):
            return jsonify({'error': '文件过大', 'code': 413}), 413
        return "文件过大", 413
    
    logger.info("错误处理器注册完成")

def register_request_hooks(app):
    """注册请求钩子"""
    
    @app.before_request
    def log_request_info():
        """记录请求信息"""
        if not request.path.startswith('/static/'):
            logger.debug(f"请求: {request.method} {request.path}")
    
    @app.after_request
    def log_response_info(response):
        """记录响应信息"""
        if not request.path.startswith('/static/'):
            logger.debug(f"响应: {response.status_code}")
        return response

def init_extensions(app):
    """初始化扩展"""
    
    # 这里可以初始化其他Flask扩展
    # 例如：数据库、缓存等
    
    logger.info("扩展初始化完成")
