#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于OpenCV的人脸检测算法
"""

import cv2
import numpy as np
from typing import List
from loguru import logger

from ..base.face_detector_base import FaceDetectorBase, FaceBox

class OpenCVDetector(FaceDetectorBase):
    """基于OpenCV Haar级联分类器的人脸检测"""
    
    def __init__(self, confidence_threshold: float = 0.5, scale_factor: float = 1.1, min_neighbors: int = 5):
        """
        初始化OpenCV人脸检测器
        
        Args:
            confidence_threshold: 置信度阈值
            scale_factor: 图像缩放因子
            min_neighbors: 最小邻居数
        """
        super().__init__("OpenCV_Haar", confidence_threshold)
        self.scale_factor = scale_factor
        self.min_neighbors = min_neighbors
        self.face_cascade = None
    
    def initialize(self) -> bool:
        """初始化OpenCV人脸检测器"""
        try:
            # 加载Haar级联分类器
            cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            self.face_cascade = cv2.CascadeClassifier(cascade_path)
            
            if self.face_cascade.empty():
                logger.error("无法加载Haar级联分类器")
                return False
            
            logger.info(f"OpenCV人脸检测器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"OpenCV人脸检测器初始化失败: {e}")
            return False
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """图像预处理"""
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 直方图均衡化
        gray = cv2.equalizeHist(gray)
        
        return gray
    
    def detect_faces(self, image: np.ndarray) -> List[FaceBox]:
        """检测人脸"""
        try:
            # 检测人脸
            faces = self.face_cascade.detectMultiScale(
                image,
                scaleFactor=self.scale_factor,
                minNeighbors=self.min_neighbors,
                minSize=(30, 30),
                flags=cv2.CASCADE_SCALE_IMAGE
            )
            
            face_boxes = []
            for (x, y, w, h) in faces:
                # OpenCV Haar分类器没有置信度，使用固定值
                confidence = 0.8
                
                face_box = FaceBox(
                    x=int(x),
                    y=int(y),
                    width=int(w),
                    height=int(h),
                    confidence=confidence
                )
                face_boxes.append(face_box)
            
            logger.debug(f"OpenCV检测到 {len(face_boxes)} 张人脸")
            return face_boxes
            
        except Exception as e:
            logger.error(f"OpenCV人脸检测失败: {e}")
            return []
    
    def get_algorithm_info(self):
        """获取算法信息"""
        info = super().get_algorithm_info()
        info.update({
            'scale_factor': self.scale_factor,
            'min_neighbors': self.min_neighbors,
            'description': 'OpenCV Haar级联分类器人脸检测'
        })
        return info

class OpenCVDNNDetector(FaceDetectorBase):
    """基于OpenCV DNN的人脸检测"""
    
    def __init__(self, confidence_threshold: float = 0.5, model_path: str = None):
        """
        初始化OpenCV DNN人脸检测器
        
        Args:
            confidence_threshold: 置信度阈值
            model_path: 模型文件路径
        """
        super().__init__("OpenCV_DNN", confidence_threshold)
        self.model_path = model_path
        self.net = None
        self.input_size = (300, 300)
    
    def initialize(self) -> bool:
        """初始化DNN模型"""
        try:
            if self.model_path:
                # 使用自定义模型路径
                prototxt_path = self.model_path + "/deploy.prototxt"
                model_path = self.model_path + "/res10_300x300_ssd_iter_140000.caffemodel"
            else:
                # 使用默认模型（需要下载）
                logger.warning("未指定模型路径，需要手动下载OpenCV DNN模型")
                return False
            
            self.net = cv2.dnn.readNetFromCaffe(prototxt_path, model_path)
            
            logger.info("OpenCV DNN人脸检测器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"OpenCV DNN人脸检测器初始化失败: {e}")
            return False
    
    def detect_faces(self, image: np.ndarray) -> List[FaceBox]:
        """检测人脸"""
        try:
            h, w = image.shape[:2]
            
            # 创建blob
            blob = cv2.dnn.blobFromImage(
                image, 1.0, self.input_size, (104.0, 177.0, 123.0)
            )
            
            # 设置输入
            self.net.setInput(blob)
            
            # 前向传播
            detections = self.net.forward()
            
            face_boxes = []
            for i in range(detections.shape[2]):
                confidence = detections[0, 0, i, 2]
                
                if confidence > self.confidence_threshold:
                    # 获取边界框坐标
                    box = detections[0, 0, i, 3:7] * np.array([w, h, w, h])
                    x, y, x2, y2 = box.astype(int)
                    
                    face_box = FaceBox(
                        x=max(0, x),
                        y=max(0, y),
                        width=max(0, x2 - x),
                        height=max(0, y2 - y),
                        confidence=float(confidence)
                    )
                    face_boxes.append(face_box)
            
            logger.debug(f"OpenCV DNN检测到 {len(face_boxes)} 张人脸")
            return face_boxes
            
        except Exception as e:
            logger.error(f"OpenCV DNN人脸检测失败: {e}")
            return []
