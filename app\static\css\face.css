/* 人脸检测和比对界面样式 */

/* 图像显示区域 */
.image-container {
    position: relative;
    display: inline-block;
    max-width: 100%;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.image-display {
    max-width: 100%;
    height: auto;
    display: block;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

/* 人脸框 */
.face-box {
    position: absolute;
    border: 2px solid var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
    pointer-events: auto;
    cursor: pointer;
    transition: all 0.2s;
}

.face-box:hover {
    border-color: var(--primary-hover);
    background: rgba(37, 99, 235, 0.2);
    box-shadow: 0 0 10px rgba(37, 99, 235, 0.3);
}

.face-box.selected {
    border-color: var(--success-color);
    background: rgba(16, 185, 129, 0.1);
}

.face-label {
    position: absolute;
    top: -25px;
    left: 0;
    background: var(--primary-color);
    color: white;
    padding: 2px 6px;
    font-size: 0.75rem;
    border-radius: var(--radius-sm);
    white-space: nowrap;
}

.face-confidence {
    font-size: 0.7rem;
    opacity: 0.9;
}

/* 检测结果 */
.detection-results {
    margin-top: var(--spacing-lg);
}

.result-summary {
    background: var(--bg-tertiary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-lg);
}

.result-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    display: block;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* 人脸列表 */
.face-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--spacing-md);
}

.face-item {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    background: var(--bg-primary);
    text-align: center;
    transition: all 0.2s;
    cursor: pointer;
}

.face-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.face-item.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.face-thumbnail {
    width: 100%;
    height: 100px;
    object-fit: cover;
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-sm);
}

.face-info {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.face-id {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

/* 比对界面 */
.comparison-container {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: var(--spacing-xl);
    align-items: center;
    margin: var(--spacing-xl) 0;
}

.comparison-side {
    text-align: center;
}

.comparison-vs {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-muted);
    padding: var(--spacing-md);
}

.comparison-image {
    max-width: 300px;
    max-height: 300px;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.comparison-label {
    margin-top: var(--spacing-md);
    font-weight: 600;
    color: var(--text-primary);
}

/* 相似度结果 */
.similarity-results {
    margin-top: var(--spacing-xl);
}

.similarity-score {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-lg);
}

.score-value {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
}

.score-value.high {
    color: var(--success-color);
}

.score-value.medium {
    color: var(--warning-color);
}

.score-value.low {
    color: var(--error-color);
}

.score-label {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.match-status {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.875rem;
}

.match-status.match {
    background: var(--success-color);
    color: white;
}

.match-status.no-match {
    background: var(--error-color);
    color: white;
}

/* 算法结果详情 */
.algorithm-results {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
}

.algorithm-result {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    background: var(--bg-primary);
}

.algorithm-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.algorithm-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-xs) 0;
}

.metric-label {
    color: var(--text-secondary);
}

.metric-value {
    font-weight: 500;
    color: var(--text-primary);
}

/* 进度指示器 */
.progress-indicator {
    text-align: center;
    padding: var(--spacing-xl);
}

.progress-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

.progress-text {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* 算法选择器 */
.algorithm-selector {
    margin-bottom: var(--spacing-lg);
}

.algorithm-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.algorithm-option {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: all 0.2s;
    background: var(--bg-primary);
}

.algorithm-option:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.algorithm-option.selected {
    border-color: var(--primary-color);
    background: var(--primary-light);
}

.algorithm-option input[type="checkbox"] {
    margin-right: var(--spacing-sm);
}

.algorithm-title {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.algorithm-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* 批量比对 */
.batch-comparison {
    margin-top: var(--spacing-xl);
}

.reference-image {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.target-images {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.target-item {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    background: var(--bg-primary);
    position: relative;
}

.target-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-sm);
}

.target-score {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
}

.target-rank {
    text-align: center;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .comparison-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .comparison-vs {
        transform: rotate(90deg);
        font-size: 1.5rem;
    }
    
    .algorithm-results {
        grid-template-columns: 1fr;
    }
    
    .face-list {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .target-images {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (max-width: 480px) {
    .score-value {
        font-size: 2rem;
    }
    
    .comparison-image {
        max-width: 250px;
        max-height: 250px;
    }
    
    .result-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}
