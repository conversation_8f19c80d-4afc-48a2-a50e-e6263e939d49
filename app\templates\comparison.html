{% extends "base.html" %}

{% block title %}人脸比对 - FaceMatchPro{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- 页面标题 -->
    <div class="text-center mb-6">
        <h1 class="text-2xl font-bold mb-2">⚖️ 人脸比对</h1>
        <p class="text-secondary">使用先进算法计算人脸相似度</p>
    </div>
    
    <!-- 比对配置 -->
    <div class="grid grid-cols-2 gap-6 mb-6">
        <!-- 人脸选择 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">选择人脸</h2>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label class="form-label">第一张人脸:</label>
                    <select id="face1-select" class="form-control form-select">
                        <option value="">请先检测人脸...</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">第二张人脸:</label>
                    <select id="face2-select" class="form-control form-select">
                        <option value="">请先检测人脸...</option>
                    </select>
                </div>
                
                <div class="flex gap-2 mt-4">
                    <a href="{{ url_for('views.detection') }}" class="btn btn-outline btn-sm">
                        👤 检测人脸
                    </a>
                    <button class="btn btn-secondary btn-sm" onclick="refreshFaceList()">
                        🔄 刷新列表
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 算法配置 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">比对算法</h2>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label class="form-label">选择算法:</label>
                    <select id="calculator-select" class="form-control form-select">
                        <option value="">加载中...</option>
                    </select>
                </div>
                
                <div class="flex gap-2 mt-4">
                    <button id="compare-btn" class="btn btn-primary">
                        ⚖️ 开始比对
                    </button>
                    <button id="batch-compare-btn" class="btn btn-secondary">
                        📊 批量比对
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 人脸预览 -->
    <div class="grid grid-cols-2 gap-6 mb-6">
        <!-- 第一张人脸 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">人脸 A</h2>
            </div>
            <div class="card-body">
                <div id="face1-preview" class="face-preview">
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M7.07,18.28C7.5,17.38 10.12,16.5 12,16.5C13.88,16.5 16.5,17.38 16.93,18.28C15.57,19.36 13.86,20 12,20C10.14,20 8.43,19.36 7.07,18.28M18.36,16.83C16.93,15.09 13.46,14.5 12,14.5C10.54,14.5 7.07,15.09 5.64,16.83C4.62,15.5 4,13.82 4,12C4,13.82 19.38,15.5 18.36,16.83M12,6C10.06,6 8.5,7.56 8.5,9.5C8.5,11.44 10.06,13 12,13C13.94,13 15.5,11.44 15.5,9.5C15.5,7.56 13.94,6 12,6M12,11A1.5,1.5 0 0,1 10.5,9.5A1.5,1.5 0 0,1 12,8A1.5,1.5 0 0,1 13.5,9.5A1.5,1.5 0 0,1 12,11Z" />
                            </svg>
                        </div>
                        <div class="empty-state-text">请选择人脸</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 第二张人脸 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">人脸 B</h2>
            </div>
            <div class="card-body">
                <div id="face2-preview" class="face-preview">
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M7.07,18.28C7.5,17.38 10.12,16.5 12,16.5C13.88,16.5 16.5,17.38 16.93,18.28C15.57,19.36 13.86,20 12,20C10.14,20 8.43,19.36 7.07,18.28M18.36,16.83C16.93,15.09 13.46,14.5 12,14.5C10.54,14.5 7.07,15.09 5.64,16.83C4.62,15.5 4,13.82 4,12C4,13.82 19.38,15.5 18.36,16.83M12,6C10.06,6 8.5,7.56 8.5,9.5C8.5,11.44 10.06,13 12,13C13.94,13 15.5,11.44 15.5,9.5C15.5,7.56 13.94,6 12,6M12,11A1.5,1.5 0 0,1 10.5,9.5A1.5,1.5 0 0,1 12,8A1.5,1.5 0 0,1 13.5,9.5A1.5,1.5 0 0,1 12,11Z" />
                            </svg>
                        </div>
                        <div class="empty-state-text">请选择人脸</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 比对结果 -->
    <div class="card mb-6">
        <div class="card-header">
            <div class="flex justify-between items-center">
                <h2 class="card-title">比对结果</h2>
                <button id="save-result-btn" class="btn btn-sm btn-success" disabled>
                    💾 保存结果
                </button>
            </div>
        </div>
        <div class="card-body">
            <div id="comparison-results">
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9,12L11,14L15,10L20,15H4L9,12Z" />
                        </svg>
                    </div>
                    <div class="empty-state-text">暂无比对结果</div>
                    <div class="empty-state-hint">请先选择两张人脸并开始比对</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 高级功能 -->
    <div class="grid grid-cols-3 gap-4 mb-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">阈值测试</h3>
            </div>
            <div class="card-body">
                <p class="text-sm text-secondary mb-4">测试不同阈值下的识别效果</p>
                <button class="btn btn-outline btn-sm w-full" onclick="showThresholdTest()">
                    🎯 开始测试
                </button>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">相似度矩阵</h3>
            </div>
            <div class="card-body">
                <p class="text-sm text-secondary mb-4">生成多张人脸的相似度矩阵</p>
                <button class="btn btn-outline btn-sm w-full" onclick="showSimilarityMatrix()">
                    📊 生成矩阵
                </button>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">算法对比</h3>
            </div>
            <div class="card-body">
                <p class="text-sm text-secondary mb-4">对比不同算法的性能</p>
                <button class="btn btn-outline btn-sm w-full" onclick="showAlgorithmComparison()">
                    ⚔️ 开始对比
                </button>
            </div>
        </div>
    </div>
    
    <!-- 比对历史 -->
    <div class="card">
        <div class="card-header">
            <div class="flex justify-between items-center">
                <h2 class="card-title">比对历史</h2>
                <div class="flex gap-2">
                    <button class="btn btn-sm btn-outline" onclick="loadComparisonHistory()">
                        🔄 刷新
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="clearComparisonHistory()">
                        🗑️ 清空历史
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div id="comparison-history">
                <div class="empty-state">
                    <div class="empty-state-text">暂无比对历史</div>
                    <div class="empty-state-hint">完成人脸比对后将显示历史记录</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 比对历史数据
let comparisonHistory = [];

// 刷新人脸列表
async function refreshFaceList() {
    if (window.app && window.app.faceComparison) {
        await window.app.faceComparison.loadFaces();
        window.app.showNotification('人脸列表已刷新', 'success');
    }
}

// 阈值测试
function showThresholdTest() {
    const content = `
        <div class="threshold-test">
            <h4>阈值测试</h4>
            <p class="text-secondary mb-4">测试不同相似度阈值下的识别效果</p>
            
            <div class="form-group">
                <label class="form-label">起始阈值:</label>
                <input type="number" class="form-control" id="start-threshold" min="0" max="1" step="0.1" value="0.1">
            </div>
            
            <div class="form-group">
                <label class="form-label">结束阈值:</label>
                <input type="number" class="form-control" id="end-threshold" min="0" max="1" step="0.1" value="0.9">
            </div>
            
            <div class="form-group">
                <label class="form-label">步长:</label>
                <input type="number" class="form-control" id="threshold-step" min="0.01" max="0.1" step="0.01" value="0.1">
            </div>
            
            <div id="threshold-results" class="mt-4" style="display: none;">
                <h5>测试结果:</h5>
                <div id="threshold-chart" style="height: 200px; border: 1px solid var(--border-color); border-radius: 4px; margin-top: 8px;"></div>
            </div>
        </div>
    `;
    
    window.app.showModal('阈值测试', content, [
        { text: '开始测试', class: 'btn-primary', handler: runThresholdTest },
        { text: '取消', class: 'btn-secondary' }
    ]);
}

// 执行阈值测试
async function runThresholdTest() {
    const startThreshold = parseFloat(document.getElementById('start-threshold').value);
    const endThreshold = parseFloat(document.getElementById('end-threshold').value);
    const step = parseFloat(document.getElementById('threshold-step').value);
    
    if (startThreshold >= endThreshold) {
        window.app.showNotification('起始阈值必须小于结束阈值', 'error');
        return;
    }
    
    try {
        const response = await window.app.apiRequest('/comparison/threshold-test', {
            method: 'POST',
            body: JSON.stringify({
                start_threshold: startThreshold,
                end_threshold: endThreshold,
                step: step
            })
        });
        
        if (response.success) {
            document.getElementById('threshold-results').style.display = 'block';
            
            // 简单的结果显示
            const chartDiv = document.getElementById('threshold-chart');
            chartDiv.innerHTML = `
                <div style="padding: 16px; text-align: center;">
                    <div>测试完成</div>
                    <div class="text-sm text-secondary mt-2">
                        测试了 ${response.data.test_count} 个阈值点
                    </div>
                </div>
            `;
            
            window.app.showNotification('阈值测试完成', 'success');
        }
    } catch (error) {
        window.app.showNotification('阈值测试失败: ' + error.message, 'error');
    }
}

// 相似度矩阵
function showSimilarityMatrix() {
    const content = `
        <div class="similarity-matrix">
            <h4>相似度矩阵</h4>
            <p class="text-secondary mb-4">生成多张人脸之间的相似度矩阵</p>
            
            <div class="form-group">
                <label class="form-label">选择人脸 (至少2张):</label>
                <div id="face-selection" style="max-height: 200px; overflow-y: auto; border: 1px solid var(--border-color); border-radius: 4px; padding: 8px;">
                    <!-- 人脸选择列表将动态生成 -->
                </div>
            </div>
            
            <div id="matrix-results" class="mt-4" style="display: none;">
                <h5>相似度矩阵:</h5>
                <div id="matrix-display" style="overflow-x: auto; margin-top: 8px;"></div>
            </div>
        </div>
    `;
    
    const modal = window.app.showModal('相似度矩阵', content, [
        { text: '生成矩阵', class: 'btn-primary', handler: generateSimilarityMatrix },
        { text: '取消', class: 'btn-secondary' }
    ]);
    
    // 加载人脸选择列表
    loadFaceSelectionList();
}

// 加载人脸选择列表
function loadFaceSelectionList() {
    // 这里应该从API获取可用的人脸列表
    const faceSelection = document.getElementById('face-selection');
    if (!faceSelection) return;
    
    // 模拟人脸列表
    const faces = [
        { id: 'face1', name: 'image1.jpg - 人脸1' },
        { id: 'face2', name: 'image1.jpg - 人脸2' },
        { id: 'face3', name: 'image2.jpg - 人脸1' }
    ];
    
    faceSelection.innerHTML = faces.map(face => `
        <div style="padding: 4px;">
            <label style="display: flex; align-items: center; cursor: pointer;">
                <input type="checkbox" value="${face.id}" style="margin-right: 8px;">
                ${face.name}
            </label>
        </div>
    `).join('');
}

// 生成相似度矩阵
async function generateSimilarityMatrix() {
    const selectedFaces = Array.from(document.querySelectorAll('#face-selection input:checked'))
        .map(input => input.value);
    
    if (selectedFaces.length < 2) {
        window.app.showNotification('请至少选择2张人脸', 'error');
        return;
    }
    
    try {
        const response = await window.app.apiRequest('/comparison/matrix', {
            method: 'POST',
            body: JSON.stringify({
                face_ids: selectedFaces
            })
        });
        
        if (response.success) {
            document.getElementById('matrix-results').style.display = 'block';
            
            // 显示矩阵
            const matrixDisplay = document.getElementById('matrix-display');
            const matrix = response.data.matrix;
            
            let tableHTML = '<table style="border-collapse: collapse; width: 100%;">';
            tableHTML += '<tr><th style="border: 1px solid var(--border-color); padding: 8px;"></th>';
            
            // 表头
            selectedFaces.forEach((face, i) => {
                tableHTML += `<th style="border: 1px solid var(--border-color); padding: 8px;">人脸${i+1}</th>`;
            });
            tableHTML += '</tr>';
            
            // 表格内容
            matrix.forEach((row, i) => {
                tableHTML += `<tr><th style="border: 1px solid var(--border-color); padding: 8px;">人脸${i+1}</th>`;
                row.forEach(value => {
                    const color = value > 0.7 ? 'var(--success-color)' : 
                                 value > 0.5 ? 'var(--warning-color)' : 'var(--error-color)';
                    tableHTML += `<td style="border: 1px solid var(--border-color); padding: 8px; text-align: center; background-color: ${color}20;">${value.toFixed(3)}</td>`;
                });
                tableHTML += '</tr>';
            });
            tableHTML += '</table>';
            
            matrixDisplay.innerHTML = tableHTML;
            window.app.showNotification('相似度矩阵生成完成', 'success');
        }
    } catch (error) {
        window.app.showNotification('生成矩阵失败: ' + error.message, 'error');
    }
}

// 算法对比
function showAlgorithmComparison() {
    const content = `
        <div class="algorithm-comparison">
            <h4>算法对比</h4>
            <p class="text-secondary mb-4">对比不同算法在相同人脸对上的性能</p>
            
            <div class="form-group">
                <label class="form-label">选择算法:</label>
                <div id="algorithm-selection" style="max-height: 150px; overflow-y: auto; border: 1px solid var(--border-color); border-radius: 4px; padding: 8px;">
                    <!-- 算法选择列表将动态生成 -->
                </div>
            </div>
            
            <div id="comparison-results" class="mt-4" style="display: none;">
                <h5>对比结果:</h5>
                <div id="comparison-chart" style="margin-top: 8px;"></div>
            </div>
        </div>
    `;
    
    const modal = window.app.showModal('算法对比', content, [
        { text: '开始对比', class: 'btn-primary', handler: runAlgorithmComparison },
        { text: '取消', class: 'btn-secondary' }
    ]);
    
    // 加载算法列表
    loadAlgorithmSelectionList();
}

// 加载算法选择列表
function loadAlgorithmSelectionList() {
    const algorithmSelection = document.getElementById('algorithm-selection');
    if (!algorithmSelection) return;
    
    // 模拟算法列表
    const algorithms = [
        { id: 'cosine', name: '余弦相似度' },
        { id: 'euclidean', name: '欧几里得距离' },
        { id: 'facenet', name: 'FaceNet' },
        { id: 'arcface', name: 'ArcFace' }
    ];
    
    algorithmSelection.innerHTML = algorithms.map(algo => `
        <div style="padding: 4px;">
            <label style="display: flex; align-items: center; cursor: pointer;">
                <input type="checkbox" value="${algo.id}" style="margin-right: 8px;" checked>
                ${algo.name}
            </label>
        </div>
    `).join('');
}

// 执行算法对比
async function runAlgorithmComparison() {
    const selectedAlgorithms = Array.from(document.querySelectorAll('#algorithm-selection input:checked'))
        .map(input => input.value);
    
    if (selectedAlgorithms.length < 2) {
        window.app.showNotification('请至少选择2个算法', 'error');
        return;
    }
    
    try {
        // 这里应该调用实际的API
        window.app.showNotification('算法对比功能开发中...', 'info');
        
        // 模拟结果显示
        document.getElementById('comparison-results').style.display = 'block';
        const chartDiv = document.getElementById('comparison-chart');
        chartDiv.innerHTML = `
            <div style="padding: 16px;">
                <div>算法对比结果:</div>
                ${selectedAlgorithms.map(algo => `
                    <div style="margin: 8px 0; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                        <strong>${algo}</strong>: 相似度 0.${Math.floor(Math.random() * 900 + 100)}
                    </div>
                `).join('')}
            </div>
        `;
        
    } catch (error) {
        window.app.showNotification('算法对比失败: ' + error.message, 'error');
    }
}

// 加载比对历史
function loadComparisonHistory() {
    const stored = localStorage.getItem('comparisonHistory');
    if (stored) {
        comparisonHistory = JSON.parse(stored);
    }
    
    renderComparisonHistory();
}

// 渲染比对历史
function renderComparisonHistory() {
    const historyContainer = document.getElementById('comparison-history');
    if (!historyContainer) return;
    
    if (comparisonHistory.length === 0) {
        historyContainer.innerHTML = `
            <div class="empty-state">
                <div class="empty-state-text">暂无比对历史</div>
                <div class="empty-state-hint">完成人脸比对后将显示历史记录</div>
            </div>
        `;
        return;
    }
    
    historyContainer.innerHTML = `
        <div class="history-list">
            ${comparisonHistory.slice(-10).reverse().map((item, index) => `
                <div class="history-item" style="display: flex; justify-content: space-between; align-items: center; padding: 12px; border-bottom: 1px solid var(--border-color);">
                    <div class="history-info">
                        <div class="history-faces">${item.face1} vs ${item.face2}</div>
                        <div class="history-meta" style="font-size: 0.875rem; color: var(--text-secondary);">
                            ${item.calculator} • 相似度: ${item.similarity} • ${item.timestamp}
                        </div>
                    </div>
                    <div class="history-result">
                        <span class="similarity-score" style="padding: 4px 8px; border-radius: 4px; font-size: 0.875rem; font-weight: bold; color: white; background: ${item.similarity > 0.7 ? 'var(--success-color)' : item.similarity > 0.5 ? 'var(--warning-color)' : 'var(--error-color)'};">
                            ${(item.similarity * 100).toFixed(1)}%
                        </span>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

// 清空比对历史
function clearComparisonHistory() {
    if (!confirm('确定要清空所有比对历史吗？')) return;
    
    comparisonHistory = [];
    localStorage.removeItem('comparisonHistory');
    renderComparisonHistory();
    window.app.showNotification('比对历史已清空', 'success');
}

// 添加比对历史记录
function addComparisonHistory(face1, face2, calculator, similarity) {
    const historyItem = {
        face1,
        face2,
        calculator,
        similarity,
        timestamp: new Date().toLocaleString('zh-CN')
    };
    
    comparisonHistory.push(historyItem);
    
    // 只保留最近100条记录
    if (comparisonHistory.length > 100) {
        comparisonHistory = comparisonHistory.slice(-100);
    }
    
    // 保存到localStorage
    localStorage.setItem('comparisonHistory', JSON.stringify(comparisonHistory));
    
    renderComparisonHistory();
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    loadComparisonHistory();
});
</script>
{% endblock %}

{% block page_init %}
// 比对页面特定初始化
console.log('人脸比对页面已加载');
{% endblock %}
