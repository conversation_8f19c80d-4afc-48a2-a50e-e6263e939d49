// 人脸比对组件

class FaceComparison {
    constructor(app) {
        this.app = app;
        this.face1 = null;
        this.face2 = null;
        this.selectedCalculators = [];
        this.comparisonResults = null;
        
        this.init();
    }
    
    init() {
        this.loadCalculators();
        this.setupEventListeners();
        this.loadFaceImages();
    }
    
    setupEventListeners() {
        // 人脸1选择
        const face1Select = document.querySelector('#face1-select');
        if (face1Select) {
            face1Select.addEventListener('change', (e) => {
                this.selectFace1(e.target.value);
            });
        }
        
        // 人脸2选择
        const face2Select = document.querySelector('#face2-select');
        if (face2Select) {
            face2Select.addEventListener('change', (e) => {
                this.selectFace2(e.target.value);
            });
        }
        
        // 比对按钮
        const compareBtn = document.querySelector('#compare-btn');
        if (compareBtn) {
            compareBtn.addEventListener('click', () => {
                this.compareFaces();
            });
        }
        
        // 批量比对按钮
        const batchCompareBtn = document.querySelector('#batch-compare-btn');
        if (batchCompareBtn) {
            batchCompareBtn.addEventListener('click', () => {
                this.showBatchComparisonModal();
            });
        }
        
        // 算法选择
        const calculatorCheckboxes = document.querySelectorAll('input[name="calculators"]');
        calculatorCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateSelectedCalculators();
            });
        });
        
        // 阈值测试按钮
        const thresholdTestBtn = document.querySelector('#threshold-test-btn');
        if (thresholdTestBtn) {
            thresholdTestBtn.addEventListener('click', () => {
                this.testThresholds();
            });
        }
    }
    
    async loadCalculators() {
        try {
            const response = await this.app.apiRequest('/comparison/calculators');
            if (response.success) {
                this.renderCalculatorOptions(response.data);
            }
        } catch (error) {
            console.error('Failed to load calculators:', error);
            this.app.showNotification('加载计算器失败', 'error');
        }
    }
    
    renderCalculatorOptions(data) {
        const calculatorContainer = document.querySelector('#calculator-options');
        if (!calculatorContainer) return;
        
        calculatorContainer.innerHTML = data.available_calculators.map(calculator => `
            <div class="algorithm-option">
                <label>
                    <input type="checkbox" name="calculators" value="${calculator}" checked>
                    <div class="algorithm-title">${calculator}</div>
                    <div class="algorithm-description">
                        ${data.calculator_info[calculator]?.description || '暂无描述'}
                    </div>
                </label>
            </div>
        `).join('');
        
        this.updateSelectedCalculators();
    }
    
    updateSelectedCalculators() {
        const checkboxes = document.querySelectorAll('input[name="calculators"]:checked');
        this.selectedCalculators = Array.from(checkboxes).map(cb => cb.value);
    }
    
    async loadFaceImages() {
        try {
            const response = await this.app.apiRequest('/upload/list?type=face');
            if (response.success) {
                this.renderFaceOptions(response.data.files);
            }
        } catch (error) {
            console.error('Failed to load face images:', error);
        }
    }
    
    renderFaceOptions(files) {
        const face1Select = document.querySelector('#face1-select');
        const face2Select = document.querySelector('#face2-select');
        
        if (!face1Select || !face2Select) return;
        
        const options = '<option value="">选择人脸...</option>' + 
            files.map(file => 
                `<option value="${file.file_path}">${file.filename}</option>`
            ).join('');
        
        face1Select.innerHTML = options;
        face2Select.innerHTML = options;
    }
    
    selectFace1(facePath) {
        this.face1 = facePath;
        this.displayFace(facePath, '#face1-display');
        this.clearComparisonResults();
    }
    
    selectFace2(facePath) {
        this.face2 = facePath;
        this.displayFace(facePath, '#face2-display');
        this.clearComparisonResults();
    }
    
    displayFace(facePath, containerId) {
        const container = document.querySelector(containerId);
        if (!container || !facePath) return;
        
        const imageUrl = facePath.replace(/\\/g, '/').replace(/^.*\/upload\//, '/static/uploads/');
        
        container.innerHTML = `
            <div class="comparison-side">
                <img src="${imageUrl}" class="comparison-image" alt="比对人脸">
                <div class="comparison-label">${facePath.split('/').pop()}</div>
            </div>
        `;
    }
    
    async compareFaces() {
        if (!this.face1 || !this.face2) {
            this.app.showNotification('请选择两张人脸进行比对', 'warning');
            return;
        }
        
        if (this.selectedCalculators.length === 0) {
            this.app.showNotification('请至少选择一个计算算法', 'warning');
            return;
        }
        
        const compareBtn = document.querySelector('#compare-btn');
        const originalText = compareBtn.textContent;
        
        try {
            compareBtn.disabled = true;
            compareBtn.innerHTML = '<span class="loading-spinner"></span> 比对中...';
            
            const response = await this.app.apiRequest('/comparison/compare', {
                method: 'POST',
                body: JSON.stringify({
                    face1_path: this.face1,
                    face2_path: this.face2,
                    calculators: this.selectedCalculators
                })
            });
            
            if (response.success) {
                this.comparisonResults = response.data;
                this.renderComparisonResults(response.data);
                this.app.showNotification('人脸比对完成', 'success');
            }
        } catch (error) {
            this.app.showNotification('人脸比对失败: ' + error.message, 'error');
        } finally {
            compareBtn.disabled = false;
            compareBtn.textContent = originalText;
        }
    }
    
    renderComparisonResults(data) {
        const resultsContainer = document.querySelector('#comparison-results');
        if (!resultsContainer) return;
        
        const averageSimilarity = data.average_similarity;
        const isMatch = averageSimilarity >= 0.6; // 默认阈值
        
        resultsContainer.innerHTML = `
            <div class="similarity-score">
                <div class="score-value ${this.getScoreClass(averageSimilarity)}">${(averageSimilarity * 100).toFixed(1)}%</div>
                <div class="score-label">平均相似度</div>
                <div class="match-status ${isMatch ? 'match' : 'no-match'}">
                    ${isMatch ? '匹配' : '不匹配'}
                </div>
            </div>
            
            <div class="algorithm-results">
                ${Object.entries(data.results).map(([algorithm, result]) => `
                    <div class="algorithm-result">
                        <div class="algorithm-name">
                            ${algorithm}
                            <span class="metric-value">${(result.similarity * 100).toFixed(1)}%</span>
                        </div>
                        <div class="algorithm-metrics">
                            <div class="metric-item">
                                <span class="metric-label">相似度:</span>
                                <span class="metric-value">${result.similarity.toFixed(4)}</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">置信度:</span>
                                <span class="metric-value">${result.confidence.toFixed(4)}</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">处理时间:</span>
                                <span class="metric-value">${result.processing_time.toFixed(3)}s</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">匹配:</span>
                                <span class="metric-value">${result.is_match ? '是' : '否'}</span>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
            
            <div class="comparison-metadata">
                <h4>比对信息</h4>
                <div class="metadata-grid">
                    <div class="metadata-item">
                        <span class="metadata-label">人脸1:</span>
                        <span class="metadata-value">${data.face1_path.split('/').pop()}</span>
                    </div>
                    <div class="metadata-item">
                        <span class="metadata-label">人脸2:</span>
                        <span class="metadata-value">${data.face2_path.split('/').pop()}</span>
                    </div>
                    <div class="metadata-item">
                        <span class="metadata-label">算法数量:</span>
                        <span class="metadata-value">${Object.keys(data.results).length}</span>
                    </div>
                    <div class="metadata-item">
                        <span class="metadata-label">总处理时间:</span>
                        <span class="metadata-value">${data.total_processing_time.toFixed(3)}s</span>
                    </div>
                </div>
            </div>
        `;
    }
    
    getScoreClass(similarity) {
        if (similarity >= 0.8) return 'high';
        if (similarity >= 0.5) return 'medium';
        return 'low';
    }
    
    async testThresholds() {
        if (!this.comparisonResults) {
            this.app.showNotification('请先进行人脸比对', 'warning');
            return;
        }
        
        try {
            const response = await this.app.apiRequest('/comparison/threshold/test', {
                method: 'POST',
                body: JSON.stringify({
                    face1_path: this.face1,
                    face2_path: this.face2,
                    calculator_name: this.selectedCalculators[0], // 使用第一个算法
                    thresholds: [0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
                })
            });
            
            if (response.success) {
                this.showThresholdTestResults(response.data);
            }
        } catch (error) {
            this.app.showNotification('阈值测试失败: ' + error.message, 'error');
        }
    }
    
    showThresholdTestResults(data) {
        const content = `
            <div class="threshold-test-results">
                <h4>阈值测试结果</h4>
                <p>当前相似度: <strong>${(data.similarity * 100).toFixed(1)}%</strong></p>
                <div class="threshold-list">
                    ${data.threshold_tests.map(test => `
                        <div class="threshold-item ${test.is_match ? 'match' : 'no-match'}">
                            <span class="threshold-value">${test.threshold}</span>
                            <span class="threshold-result">${test.is_match ? '匹配' : '不匹配'}</span>
                            <span class="threshold-confidence">置信度: ${test.confidence.toFixed(3)}</span>
                        </div>
                    `).join('')}
                </div>
                <p class="threshold-recommendation">
                    推荐阈值: <strong>${data.recommended_threshold}</strong>
                </p>
            </div>
        `;
        
        this.app.showModal('阈值测试结果', content, [
            { text: '关闭', class: 'btn-secondary' }
        ]);
    }
    
    showBatchComparisonModal() {
        const content = `
            <div class="batch-comparison-form">
                <div class="form-group">
                    <label class="form-label">参考人脸:</label>
                    <select id="batch-reference-face" class="form-control">
                        <option value="">选择参考人脸...</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">目标人脸 (按住Ctrl多选):</label>
                    <select id="batch-target-faces" class="form-control" multiple size="6">
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">计算算法:</label>
                    <select id="batch-calculator" class="form-control">
                        ${this.selectedCalculators.map(calc => 
                            `<option value="${calc}">${calc}</option>`
                        ).join('')}
                    </select>
                </div>
            </div>
        `;
        
        const modal = this.app.showModal('批量人脸比对', content, [
            { text: '开始比对', class: 'btn-primary', handler: () => this.executeBatchComparison() },
            { text: '取消', class: 'btn-secondary' }
        ]);
        
        // 加载人脸选项
        this.loadFaceImages().then(() => {
            const referenceSelect = modal.querySelector('#batch-reference-face');
            const targetSelect = modal.querySelector('#batch-target-faces');
            
            const response = this.app.apiRequest('/upload/list?type=face');
            response.then(res => {
                if (res.success) {
                    const options = res.data.files.map(file => 
                        `<option value="${file.file_path}">${file.filename}</option>`
                    ).join('');
                    
                    referenceSelect.innerHTML = '<option value="">选择参考人脸...</option>' + options;
                    targetSelect.innerHTML = options;
                }
            });
        });
    }
    
    async executeBatchComparison() {
        const referenceFace = document.querySelector('#batch-reference-face').value;
        const targetFaces = Array.from(document.querySelector('#batch-target-faces').selectedOptions)
            .map(option => option.value);
        const calculator = document.querySelector('#batch-calculator').value;
        
        if (!referenceFace || targetFaces.length === 0) {
            this.app.showNotification('请选择参考人脸和目标人脸', 'warning');
            return;
        }
        
        try {
            this.app.showLoading('#comparison-results', '批量比对中...');
            
            const response = await this.app.apiRequest('/comparison/batch', {
                method: 'POST',
                body: JSON.stringify({
                    reference_face: referenceFace,
                    target_faces: targetFaces,
                    calculator: calculator
                })
            });
            
            if (response.success) {
                this.renderBatchResults(response.data);
                this.app.showNotification(
                    `批量比对完成: ${response.data.successful_comparisons}/${response.data.total_comparisons}`, 
                    'success'
                );
            }
        } catch (error) {
            this.app.showNotification('批量比对失败: ' + error.message, 'error');
        } finally {
            this.app.hideLoading('#comparison-results');
        }
    }
    
    renderBatchResults(data) {
        const resultsContainer = document.querySelector('#comparison-results');
        if (!resultsContainer) return;
        
        // 按相似度排序
        const sortedResults = data.results.sort((a, b) => b.similarity - a.similarity);
        
        resultsContainer.innerHTML = `
            <div class="batch-comparison">
                <div class="reference-image">
                    <h4>参考人脸</h4>
                    <img src="${data.reference_face.replace(/\\/g, '/').replace(/^.*\/upload\//, '/static/uploads/')}" 
                         class="comparison-image" alt="参考人脸">
                    <div class="comparison-label">${data.reference_face.split('/').pop()}</div>
                </div>
                
                <div class="batch-summary">
                    <div class="result-stats">
                        <div class="stat-item">
                            <span class="stat-value">${data.total_comparisons}</span>
                            <span class="stat-label">总比对数</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${data.successful_comparisons}</span>
                            <span class="stat-label">成功比对</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${data.calculator}</span>
                            <span class="stat-label">使用算法</span>
                        </div>
                    </div>
                </div>
                
                <div class="target-images">
                    ${sortedResults.map((result, index) => `
                        <div class="target-item">
                            <img src="${result.target_face.replace(/\\/g, '/').replace(/^.*\/upload\//, '/static/uploads/')}" 
                                 class="target-image" alt="目标人脸">
                            <div class="target-score ${this.getScoreClass(result.similarity)}">
                                ${(result.similarity * 100).toFixed(1)}%
                            </div>
                            <div class="target-rank">
                                排名 ${index + 1} - ${result.target_face.split('/').pop()}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }
    
    clearComparisonResults() {
        const resultsContainer = document.querySelector('#comparison-results');
        if (resultsContainer) {
            resultsContainer.innerHTML = '';
        }
        this.comparisonResults = null;
    }
}

// 扩展主应用
if (window.app) {
    window.app.initComparisonComponent = function() {
        this.faceComparison = new FaceComparison(this);
    };
}
