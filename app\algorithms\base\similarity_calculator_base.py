#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相似度计算算法基类
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Tuple, Optional
import numpy as np
from dataclasses import dataclass

@dataclass
class SimilarityResult:
    """相似度结果数据类"""
    similarity: float
    distance: float
    algorithm: str
    confidence: float
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'similarity': self.similarity,
            'distance': self.distance,
            'algorithm': self.algorithm,
            'confidence': self.confidence,
            'metadata': self.metadata or {}
        }
    
    def is_match(self, threshold: float = 0.6) -> bool:
        """判断是否匹配"""
        return self.similarity >= threshold

class SimilarityCalculatorBase(ABC):
    """相似度计算算法基类"""
    
    def __init__(self, name: str, similarity_threshold: float = 0.6):
        """
        初始化相似度计算器
        
        Args:
            name: 算法名称
            similarity_threshold: 相似度阈值
        """
        self.name = name
        self.similarity_threshold = similarity_threshold
        self._is_initialized = False
    
    @abstractmethod
    def initialize(self) -> bool:
        """
        初始化算法模型
        
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    @abstractmethod
    def extract_features(self, face_image: np.ndarray) -> np.ndarray:
        """
        提取人脸特征
        
        Args:
            face_image: 人脸图像 (BGR格式)
            
        Returns:
            np.ndarray: 特征向量
        """
        pass
    
    @abstractmethod
    def calculate_similarity(self, features1: np.ndarray, features2: np.ndarray) -> SimilarityResult:
        """
        计算两个特征向量的相似度
        
        Args:
            features1: 第一个特征向量
            features2: 第二个特征向量
            
        Returns:
            SimilarityResult: 相似度结果
        """
        pass
    
    def preprocess_face(self, face_image: np.ndarray) -> np.ndarray:
        """
        人脸图像预处理
        
        Args:
            face_image: 人脸图像
            
        Returns:
            np.ndarray: 预处理后的人脸图像
        """
        # 默认不做处理，子类可以重写
        return face_image
    
    def compare_faces(self, face1: np.ndarray, face2: np.ndarray) -> SimilarityResult:
        """
        比较两张人脸图像
        
        Args:
            face1: 第一张人脸图像
            face2: 第二张人脸图像
            
        Returns:
            SimilarityResult: 相似度结果
        """
        if not self._is_initialized:
            if not self.initialize():
                raise RuntimeError(f"算法 {self.name} 初始化失败")
            self._is_initialized = True
        
        # 预处理
        processed_face1 = self.preprocess_face(face1)
        processed_face2 = self.preprocess_face(face2)
        
        # 提取特征
        features1 = self.extract_features(processed_face1)
        features2 = self.extract_features(processed_face2)
        
        # 计算相似度
        result = self.calculate_similarity(features1, features2)
        
        return result
    
    def batch_compare(self, face1: np.ndarray, faces: list) -> list:
        """
        批量比较人脸
        
        Args:
            face1: 参考人脸图像
            faces: 待比较的人脸图像列表
            
        Returns:
            list: 相似度结果列表
        """
        results = []
        
        # 提取参考人脸特征
        if not self._is_initialized:
            if not self.initialize():
                raise RuntimeError(f"算法 {self.name} 初始化失败")
            self._is_initialized = True
        
        processed_face1 = self.preprocess_face(face1)
        features1 = self.extract_features(processed_face1)
        
        # 批量比较
        for face2 in faces:
            processed_face2 = self.preprocess_face(face2)
            features2 = self.extract_features(processed_face2)
            result = self.calculate_similarity(features1, features2)
            results.append(result)
        
        return results
    
    def get_algorithm_info(self) -> Dict[str, Any]:
        """
        获取算法信息
        
        Returns:
            Dict[str, Any]: 算法信息
        """
        return {
            'name': self.name,
            'similarity_threshold': self.similarity_threshold,
            'is_initialized': self._is_initialized,
            'type': 'similarity_calculator'
        }
    
    def set_similarity_threshold(self, threshold: float):
        """设置相似度阈值"""
        if 0.0 <= threshold <= 1.0:
            self.similarity_threshold = threshold
        else:
            raise ValueError("相似度阈值必须在0.0到1.0之间")
    
    @staticmethod
    def cosine_similarity(vec1: np.ndarray, vec2: np.ndarray) -> float:
        """计算余弦相似度"""
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    @staticmethod
    def euclidean_distance(vec1: np.ndarray, vec2: np.ndarray) -> float:
        """计算欧几里得距离"""
        return np.linalg.norm(vec1 - vec2)
    
    def __str__(self) -> str:
        return f"SimilarityCalculator({self.name})"
    
    def __repr__(self) -> str:
        return f"SimilarityCalculator(name='{self.name}', threshold={self.similarity_threshold})"
