#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于FaceNet的相似度计算算法
"""

import cv2
import numpy as np
from typing import Dict, Any
from loguru import logger

from ..base.similarity_calculator_base import SimilarityCalculatorBase, SimilarityResult

try:
    from facenet_pytorch import MTCNN, InceptionResnetV1
    import torch
    FACENET_AVAILABLE = True
except ImportError:
    FACENET_AVAILABLE = False
    logger.warning("facenet-pytorch未安装，FaceNet功能将不可用")

class FaceNetSimilarityCalculator(SimilarityCalculatorBase):
    """基于FaceNet的人脸相似度计算"""
    
    def __init__(self, similarity_threshold: float = 0.6, device: str = 'cpu'):
        """
        初始化FaceNet相似度计算器
        
        Args:
            similarity_threshold: 相似度阈值
            device: 计算设备 ('cpu' 或 'cuda')
        """
        super().__init__("FaceNet", similarity_threshold)
        self.device = device
        self.model = None
        self.mtcnn = None
    
    def initialize(self) -> bool:
        """初始化FaceNet模型"""
        if not FACENET_AVAILABLE:
            logger.error("facenet-pytorch未安装，无法初始化FaceNet")
            return False
        
        try:
            # 设置设备
            if self.device == 'cuda' and not torch.cuda.is_available():
                logger.warning("CUDA不可用，使用CPU")
                self.device = 'cpu'
            
            # 初始化MTCNN（用于人脸对齐）
            self.mtcnn = MTCNN(
                image_size=160,
                margin=0,
                min_face_size=20,
                thresholds=[0.6, 0.7, 0.7],
                factor=0.709,
                post_process=True,
                device=self.device
            )
            
            # 初始化FaceNet模型
            self.model = InceptionResnetV1(pretrained='vggface2').eval()
            self.model.to(self.device)
            
            logger.info(f"FaceNet模型初始化成功，设备: {self.device}")
            return True
            
        except Exception as e:
            logger.error(f"FaceNet模型初始化失败: {e}")
            return False
    
    def preprocess_face(self, face_image: np.ndarray) -> np.ndarray:
        """人脸图像预处理"""
        try:
            # 转换BGR到RGB
            if len(face_image.shape) == 3:
                face_rgb = cv2.cvtColor(face_image, cv2.COLOR_BGR2RGB)
            else:
                face_rgb = cv2.cvtColor(face_image, cv2.COLOR_GRAY2RGB)
            
            return face_rgb
            
        except Exception as e:
            logger.error(f"人脸预处理失败: {e}")
            return face_image
    
    def extract_features(self, face_image: np.ndarray) -> np.ndarray:
        """使用FaceNet提取人脸特征"""
        try:
            # 使用MTCNN进行人脸对齐和裁剪
            face_tensor = self.mtcnn(face_image)
            
            if face_tensor is None:
                logger.warning("MTCNN未检测到人脸，使用原始图像")
                # 手动调整大小并转换为tensor
                face_resized = cv2.resize(face_image, (160, 160))
                face_tensor = torch.from_numpy(face_resized).permute(2, 0, 1).float()
                face_tensor = (face_tensor - 127.5) / 128.0
            
            # 添加batch维度
            face_tensor = face_tensor.unsqueeze(0).to(self.device)
            
            # 提取特征
            with torch.no_grad():
                embeddings = self.model(face_tensor)
                embeddings = embeddings.cpu().numpy().flatten()
            
            # L2归一化
            embeddings = embeddings / np.linalg.norm(embeddings)
            
            return embeddings
            
        except Exception as e:
            logger.error(f"FaceNet特征提取失败: {e}")
            return np.array([])
    
    def calculate_similarity(self, features1: np.ndarray, features2: np.ndarray) -> SimilarityResult:
        """计算FaceNet特征的相似度"""
        try:
            # 计算余弦相似度
            similarity = self.cosine_similarity(features1, features2)
            
            # 计算欧几里得距离
            distance = self.euclidean_distance(features1, features2)
            
            # FaceNet的置信度基于特征向量的质量
            confidence = min(np.linalg.norm(features1), np.linalg.norm(features2))
            confidence = min(confidence, 1.0)
            
            result = SimilarityResult(
                similarity=float(similarity),
                distance=float(distance),
                algorithm=self.name,
                confidence=float(confidence),
                metadata={
                    'feature_dim': len(features1),
                    'model': 'InceptionResnetV1',
                    'pretrained': 'vggface2'
                }
            )
            
            return result
            
        except Exception as e:
            logger.error(f"FaceNet相似度计算失败: {e}")
            return SimilarityResult(
                similarity=0.0,
                distance=float('inf'),
                algorithm=self.name,
                confidence=0.0
            )
    
    def get_algorithm_info(self):
        """获取算法信息"""
        info = super().get_algorithm_info()
        info.update({
            'device': self.device,
            'model': 'InceptionResnetV1',
            'pretrained': 'vggface2',
            'feature_dim': 512,
            'description': 'FaceNet深度学习人脸识别'
        })
        return info

class SimpleFaceNetCalculator(SimilarityCalculatorBase):
    """简化版FaceNet计算器（使用face_recognition库）"""
    
    def __init__(self, similarity_threshold: float = 0.6):
        """
        初始化简化版FaceNet计算器
        
        Args:
            similarity_threshold: 相似度阈值
        """
        super().__init__("SimpleFaceNet", similarity_threshold)
        self.face_recognition = None
    
    def initialize(self) -> bool:
        """初始化face_recognition库"""
        try:
            import face_recognition
            self.face_recognition = face_recognition
            
            logger.info("SimpleFaceNet初始化成功")
            return True
            
        except ImportError:
            logger.error("face_recognition库未安装，无法初始化SimpleFaceNet")
            return False
    
    def preprocess_face(self, face_image: np.ndarray) -> np.ndarray:
        """人脸图像预处理"""
        # 转换BGR到RGB
        if len(face_image.shape) == 3:
            face_rgb = cv2.cvtColor(face_image, cv2.COLOR_BGR2RGB)
        else:
            face_rgb = cv2.cvtColor(face_image, cv2.COLOR_GRAY2RGB)
        
        return face_rgb
    
    def extract_features(self, face_image: np.ndarray) -> np.ndarray:
        """使用face_recognition提取特征"""
        try:
            # 检测人脸位置
            face_locations = self.face_recognition.face_locations(face_image)
            
            if not face_locations:
                logger.warning("未检测到人脸")
                return np.array([])
            
            # 提取第一个人脸的特征
            face_encodings = self.face_recognition.face_encodings(face_image, face_locations)
            
            if not face_encodings:
                logger.warning("特征提取失败")
                return np.array([])
            
            return face_encodings[0]
            
        except Exception as e:
            logger.error(f"SimpleFaceNet特征提取失败: {e}")
            return np.array([])
    
    def calculate_similarity(self, features1: np.ndarray, features2: np.ndarray) -> SimilarityResult:
        """计算相似度"""
        try:
            # 使用face_recognition的比较函数
            distance = self.face_recognition.face_distance([features1], features2)[0]
            
            # 转换为相似度
            similarity = 1.0 - distance
            
            # 计算置信度
            confidence = max(0.0, 1.0 - distance)
            
            result = SimilarityResult(
                similarity=float(similarity),
                distance=float(distance),
                algorithm=self.name,
                confidence=float(confidence),
                metadata={
                    'feature_dim': len(features1),
                    'library': 'face_recognition'
                }
            )
            
            return result
            
        except Exception as e:
            logger.error(f"SimpleFaceNet相似度计算失败: {e}")
            return SimilarityResult(
                similarity=0.0,
                distance=float('inf'),
                algorithm=self.name,
                confidence=0.0
            )
