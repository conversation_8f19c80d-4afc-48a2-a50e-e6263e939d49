#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FaceMatchPro 主应用程序入口
"""

import os
import sys
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def main():
    """主函数"""
    try:
        # 创建Flask应用
        app = create_app()
        
        # 获取配置
        host = app.config.get('HOST', '127.0.0.1')
        port = app.config.get('PORT', 5000)
        debug = app.config.get('DEBUG', False)
        
        logger.info(f"启动 FaceMatchPro 服务器")
        logger.info(f"访问地址: http://{host}:{port}")
        logger.info(f"调试模式: {'开启' if debug else '关闭'}")
        
        # 启动应用
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
        
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
