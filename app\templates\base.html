<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}FaceMatchPro - 人脸相似度比对工具{% endblock %}</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/upload.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/face.css') }}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    
    {% block head %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="navbar-content">
                <a href="{{ url_for('views.index') }}" class="navbar-brand">
                    🔍 FaceMatchPro
                </a>
                
                <ul class="navbar-nav">
                    <li><a href="{{ url_for('views.index') }}">首页</a></li>
                    <li><a href="{{ url_for('views.upload') }}">文件上传</a></li>
                    <li><a href="{{ url_for('views.detection') }}">人脸检测</a></li>
                    <li><a href="{{ url_for('views.comparison') }}">人脸比对</a></li>
                    <li><a href="{{ url_for('views.about') }}">关于</a></li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            {% block content %}{% endblock %}
        </div>
    </main>
    
    <!-- 页脚 -->
    <footer style="background: var(--bg-primary); border-top: 1px solid var(--border-color); padding: var(--spacing-xl) 0; margin-top: var(--spacing-2xl);">
        <div class="container">
            <div class="text-center text-secondary">
                <p>&copy; 2024 FaceMatchPro. 基于Flask和OpenCV的人脸相似度比对工具.</p>
                <p class="text-sm mt-2">
                    会话ID: <span class="session-id">{{ session.get('session_id', '未创建') }}</span>
                </p>
            </div>
        </div>
    </footer>
    
    <!-- 通知容器 -->
    <div class="notification-container"></div>
    
    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    <script src="{{ url_for('static', filename='js/face-detection.js') }}"></script>
    <script src="{{ url_for('static', filename='js/face-comparison.js') }}"></script>
    
    {% block scripts %}{% endblock %}
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 更新会话显示
            if (window.app) {
                window.app.updateSessionDisplay();
            }
            
            // 页面特定初始化
            {% block page_init %}{% endblock %}
        });
    </script>
</body>
</html>
