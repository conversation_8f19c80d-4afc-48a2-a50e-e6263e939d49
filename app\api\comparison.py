#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人脸相似度比对API
"""

from flask import Blueprint, request, jsonify, session
from loguru import logger

from app.services import FaceService
from app.services.algorithm_manager import algorithm_manager
from app.utils.response_utils import ResponseUtils

comparison_bp = Blueprint('comparison', __name__, url_prefix='/api/comparison')

# 初始化服务
face_service = FaceService()
response_utils = ResponseUtils()

@comparison_bp.route('/compare', methods=['POST'])
def compare_faces():
    """
    比较两张人脸的相似度
    
    Returns:
        JSON响应
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return response_utils.error('缺少请求参数', 400)
        
        # 检查必需参数
        required_params = ['face1_path', 'face2_path']
        for param in required_params:
            if param not in data:
                return response_utils.error(f'缺少参数: {param}', 400)
        
        face1_path = data['face1_path']
        face2_path = data['face2_path']
        calculator_names = data.get('calculators', None)
        
        # 验证会话
        session_id = session.get('session_id')
        if not session_id:
            return response_utils.error('会话不存在', 400)
        
        # 执行人脸比较
        result = face_service.compare_faces(face1_path, face2_path, calculator_names)
        
        if result['success']:
            logger.info(f"人脸比较成功: 相似度 {result['average_similarity']:.3f}")
            return response_utils.success('人脸比较成功', result)
        else:
            return response_utils.error(result['message'], 400, result)
    
    except Exception as e:
        logger.error(f"人脸比较异常: {e}")
        return response_utils.error('人脸比较失败', 500)

@comparison_bp.route('/batch', methods=['POST'])
def batch_compare():
    """
    批量人脸比较
    
    Returns:
        JSON响应
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return response_utils.error('缺少请求参数', 400)
        
        # 检查必需参数
        required_params = ['reference_face', 'target_faces']
        for param in required_params:
            if param not in data:
                return response_utils.error(f'缺少参数: {param}', 400)
        
        reference_face = data['reference_face']
        target_faces = data['target_faces']
        calculator_name = data.get('calculator', None)
        
        if not isinstance(target_faces, list) or len(target_faces) == 0:
            return response_utils.error('目标人脸列表不能为空', 400)
        
        # 验证会话
        session_id = session.get('session_id')
        if not session_id:
            return response_utils.error('会话不存在', 400)
        
        # 执行批量比较
        result = face_service.batch_compare_faces(reference_face, target_faces, calculator_name)
        
        if result['success']:
            logger.info(f"批量比较成功: {result['successful_comparisons']}/{result['total_comparisons']}")
            return response_utils.success('批量比较成功', result)
        else:
            return response_utils.error(result['message'], 400, result)
    
    except Exception as e:
        logger.error(f"批量比较异常: {e}")
        return response_utils.error('批量比较失败', 500)

@comparison_bp.route('/matrix', methods=['POST'])
def comparison_matrix():
    """
    生成人脸相似度矩阵
    
    Returns:
        JSON响应
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return response_utils.error('缺少请求参数', 400)
        
        # 检查必需参数
        if 'face_paths' not in data:
            return response_utils.error('缺少人脸路径列表参数', 400)
        
        face_paths = data['face_paths']
        calculator_name = data.get('calculator', None)
        
        if not isinstance(face_paths, list) or len(face_paths) < 2:
            return response_utils.error('至少需要2张人脸图像', 400)
        
        # 验证会话
        session_id = session.get('session_id')
        if not session_id:
            return response_utils.error('会话不存在', 400)
        
        # 获取相似度计算器
        calculator = algorithm_manager.get_similarity_calculator(calculator_name)
        
        # 生成相似度矩阵
        matrix = []
        face_count = len(face_paths)
        
        for i in range(face_count):
            row = []
            for j in range(face_count):
                if i == j:
                    # 自己与自己的相似度为1.0
                    similarity = 1.0
                elif i < j:
                    # 计算相似度
                    try:
                        result = face_service.compare_faces(
                            face_paths[i], face_paths[j], [calculator.name]
                        )
                        if result['success'] and calculator.name in result['results']:
                            similarity = result['results'][calculator.name]['similarity']
                        else:
                            similarity = 0.0
                    except Exception as e:
                        logger.error(f"计算相似度失败 {i}-{j}: {e}")
                        similarity = 0.0
                else:
                    # 使用对称性
                    similarity = matrix[j][i]
                
                row.append(round(similarity, 4))
            matrix.append(row)
        
        # 构建结果
        result = {
            'face_paths': face_paths,
            'face_count': face_count,
            'calculator': calculator.name,
            'similarity_matrix': matrix,
            'matrix_size': f"{face_count}x{face_count}"
        }
        
        logger.info(f"相似度矩阵生成成功: {face_count}x{face_count}")
        return response_utils.success('相似度矩阵生成成功', result)
    
    except Exception as e:
        logger.error(f"生成相似度矩阵异常: {e}")
        return response_utils.error('生成相似度矩阵失败', 500)

@comparison_bp.route('/calculators', methods=['GET'])
def get_calculators():
    """
    获取可用的相似度计算算法
    
    Returns:
        JSON响应
    """
    try:
        # 获取可用的计算器
        calculators = algorithm_manager.get_available_calculators()
        
        # 获取详细信息
        calculator_info = {}
        for calculator_name in calculators:
            try:
                info = algorithm_manager.get_calculator_info(calculator_name)
                calculator_info[calculator_name] = info
            except Exception as e:
                logger.warning(f"获取计算器信息失败 {calculator_name}: {e}")
        
        result = {
            'available_calculators': calculators,
            'calculator_info': calculator_info,
            'default_calculator': calculators[0] if calculators else None
        }
        
        return response_utils.success('获取计算器列表成功', result)
    
    except Exception as e:
        logger.error(f"获取计算器列表异常: {e}")
        return response_utils.error('获取计算器列表失败', 500)

@comparison_bp.route('/calculator/config', methods=['POST'])
def configure_calculator():
    """
    配置相似度计算算法参数
    
    Returns:
        JSON响应
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return response_utils.error('缺少请求参数', 400)
        
        # 检查必需参数
        required_params = ['calculator_name', 'threshold']
        for param in required_params:
            if param not in data:
                return response_utils.error(f'缺少参数: {param}', 400)
        
        calculator_name = data['calculator_name']
        threshold = data['threshold']
        
        # 验证阈值范围
        if not (0.0 <= threshold <= 1.0):
            return response_utils.error('阈值必须在0.0到1.0之间', 400)
        
        # 设置计算器阈值
        algorithm_manager.set_calculator_threshold(calculator_name, threshold)
        
        result = {
            'calculator_name': calculator_name,
            'threshold': threshold,
            'message': f'计算器 {calculator_name} 阈值已设置为 {threshold}'
        }
        
        logger.info(f"计算器配置成功: {calculator_name}, 阈值: {threshold}")
        return response_utils.success('计算器配置成功', result)
    
    except ValueError as e:
        return response_utils.error(str(e), 400)
    except Exception as e:
        logger.error(f"计算器配置异常: {e}")
        return response_utils.error('计算器配置失败', 500)

@comparison_bp.route('/threshold/test', methods=['POST'])
def test_threshold():
    """
    测试不同阈值下的匹配结果
    
    Returns:
        JSON响应
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return response_utils.error('缺少请求参数', 400)
        
        # 检查必需参数
        required_params = ['face1_path', 'face2_path', 'calculator_name']
        for param in required_params:
            if param not in data:
                return response_utils.error(f'缺少参数: {param}', 400)
        
        face1_path = data['face1_path']
        face2_path = data['face2_path']
        calculator_name = data['calculator_name']
        thresholds = data.get('thresholds', [0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9])
        
        # 验证会话
        session_id = session.get('session_id')
        if not session_id:
            return response_utils.error('会话不存在', 400)
        
        # 获取计算器
        calculator = algorithm_manager.get_similarity_calculator(calculator_name)
        
        # 计算相似度
        comparison_result = face_service.compare_faces(face1_path, face2_path, [calculator_name])
        
        if not comparison_result['success']:
            return response_utils.error('人脸比较失败', 400, comparison_result)
        
        similarity = comparison_result['results'][calculator_name]['similarity']
        
        # 测试不同阈值
        threshold_results = []
        for threshold in thresholds:
            is_match = similarity >= threshold
            threshold_results.append({
                'threshold': threshold,
                'is_match': is_match,
                'confidence': abs(similarity - threshold)
            })
        
        result = {
            'face1_path': face1_path,
            'face2_path': face2_path,
            'calculator': calculator_name,
            'similarity': similarity,
            'threshold_tests': threshold_results,
            'recommended_threshold': 0.6  # 默认推荐阈值
        }
        
        logger.info(f"阈值测试完成: 相似度 {similarity:.3f}")
        return response_utils.success('阈值测试完成', result)
    
    except Exception as e:
        logger.error(f"阈值测试异常: {e}")
        return response_utils.error('阈值测试失败', 500)

@comparison_bp.route('/statistics', methods=['GET'])
def get_comparison_statistics():
    """
    获取比较统计信息
    
    Returns:
        JSON响应
    """
    try:
        # 验证会话
        session_id = session.get('session_id')
        if not session_id:
            return response_utils.error('会话不存在', 400)
        
        # 统计信息
        statistics = {
            'session_id': session_id,
            'available_calculators': algorithm_manager.get_available_calculators(),
            'calculator_count': len(algorithm_manager.get_available_calculators()),
            'algorithms_info': algorithm_manager.get_all_algorithms_info()
        }
        
        return response_utils.success('获取统计信息成功', statistics)
    
    except Exception as e:
        logger.error(f"获取统计信息异常: {e}")
        return response_utils.error('获取统计信息失败', 500)
