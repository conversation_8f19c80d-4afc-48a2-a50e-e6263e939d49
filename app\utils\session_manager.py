#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话管理器
"""

import uuid
import time
from pathlib import Path
from flask import session, current_app
from loguru import logger

class SessionManager:
    """会话管理器类"""
    
    def __init__(self):
        self.session_key = 'facematch_session_id'
    
    def get_or_create_session(self):
        """获取或创建会话ID"""
        
        session_id = session.get(self.session_key)
        
        if not session_id:
            session_id = self._generate_session_id()
            session[self.session_key] = session_id
            session.permanent = True
            
            # 创建会话目录
            self._create_session_directories(session_id)
            
            logger.info(f"创建新会话: {session_id}")
        
        return session_id
    
    def _generate_session_id(self):
        """生成唯一会话ID"""
        timestamp = int(time.time())
        unique_id = str(uuid.uuid4()).replace('-', '')[:8]
        return f"session_{timestamp}_{unique_id}"
    
    def _create_session_directories(self, session_id):
        """为会话创建目录"""
        try:
            base_dirs = [
                current_app.config['UPLOAD_FOLDER'],
                current_app.config['TEMP_FOLDER']
            ]
            
            for base_dir in base_dirs:
                session_dir = Path(base_dir) / session_id
                session_dir.mkdir(parents=True, exist_ok=True)
                
                # 创建子目录
                subdirs = ['original', 'processed', 'faces', 'results']
                for subdir in subdirs:
                    (session_dir / subdir).mkdir(exist_ok=True)
            
            logger.debug(f"会话目录已创建: {session_id}")
            
        except Exception as e:
            logger.error(f"创建会话目录失败: {e}")
            raise
    
    def get_session_path(self, session_id, folder_type='upload', subdir=None):
        """获取会话路径"""
        
        if folder_type == 'upload':
            base_path = Path(current_app.config['UPLOAD_FOLDER'])
        elif folder_type == 'temp':
            base_path = Path(current_app.config['TEMP_FOLDER'])
        else:
            raise ValueError(f"不支持的文件夹类型: {folder_type}")
        
        session_path = base_path / session_id
        
        if subdir:
            session_path = session_path / subdir
        
        return session_path
    
    def cleanup_session(self, session_id):
        """清理会话文件"""
        try:
            for folder_type in ['upload', 'temp']:
                session_path = self.get_session_path(session_id, folder_type)
                if session_path.exists():
                    import shutil
                    shutil.rmtree(session_path)
                    logger.info(f"已清理会话文件: {session_path}")
        
        except Exception as e:
            logger.error(f"清理会话文件失败: {e}")
    
    def get_current_session_id(self):
        """获取当前会话ID"""
        return session.get(self.session_key)
    
    def is_valid_session(self, session_id):
        """检查会话是否有效"""
        if not session_id:
            return False
        
        # 检查会话目录是否存在
        upload_path = self.get_session_path(session_id, 'upload')
        return upload_path.exists()
