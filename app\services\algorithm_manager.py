#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法管理器
"""

from typing import Dict, List, Any, Optional
from loguru import logger

from app.algorithms.detection import OpenCVDetector, DlibDetector
from app.algorithms.similarity import (
    CosineSimilarityCalculator, 
    EuclideanSimilarityCalculator,
    FaceNetSimilarityCalculator,
    ArcFaceSimilarityCalculator
)

class AlgorithmManager:
    """算法管理器 - 统一管理所有人脸检测和相似度计算算法"""
    
    def __init__(self):
        self.face_detectors = {}
        self.similarity_calculators = {}
        self._initialize_algorithms()
    
    def _initialize_algorithms(self):
        """初始化所有算法"""
        logger.info("开始初始化算法管理器...")
        
        # 初始化人脸检测算法
        self._initialize_face_detectors()
        
        # 初始化相似度计算算法
        self._initialize_similarity_calculators()
        
        logger.info(f"算法管理器初始化完成，"
                   f"人脸检测算法: {len(self.face_detectors)}个，"
                   f"相似度算法: {len(self.similarity_calculators)}个")
    
    def _initialize_face_detectors(self):
        """初始化人脸检测算法"""
        detectors = [
            OpenCVDetector(confidence_threshold=0.5),
            # DlibDetector(confidence_threshold=0.5),  # 可选，需要dlib
        ]
        
        for detector in detectors:
            try:
                if detector.initialize():
                    self.face_detectors[detector.name] = detector
                    logger.info(f"人脸检测算法 {detector.name} 初始化成功")
                else:
                    logger.warning(f"人脸检测算法 {detector.name} 初始化失败")
            except Exception as e:
                logger.error(f"人脸检测算法 {detector.name} 初始化异常: {e}")
    
    def _initialize_similarity_calculators(self):
        """初始化相似度计算算法"""
        calculators = [
            CosineSimilarityCalculator(similarity_threshold=0.6),
            EuclideanSimilarityCalculator(similarity_threshold=0.6),
            # FaceNetSimilarityCalculator(similarity_threshold=0.6),  # 可选，需要facenet-pytorch
            # ArcFaceSimilarityCalculator(similarity_threshold=0.6),  # 占位符实现
        ]
        
        for calculator in calculators:
            try:
                if calculator.initialize():
                    self.similarity_calculators[calculator.name] = calculator
                    logger.info(f"相似度算法 {calculator.name} 初始化成功")
                else:
                    logger.warning(f"相似度算法 {calculator.name} 初始化失败")
            except Exception as e:
                logger.error(f"相似度算法 {calculator.name} 初始化异常: {e}")
    
    def get_face_detector(self, name: str = None):
        """
        获取人脸检测器
        
        Args:
            name: 算法名称，如果为None则返回默认算法
            
        Returns:
            人脸检测器实例
        """
        if name is None:
            # 返回第一个可用的检测器
            if self.face_detectors:
                return next(iter(self.face_detectors.values()))
            else:
                raise RuntimeError("没有可用的人脸检测算法")
        
        if name not in self.face_detectors:
            raise ValueError(f"人脸检测算法 '{name}' 不存在")
        
        return self.face_detectors[name]
    
    def get_similarity_calculator(self, name: str = None):
        """
        获取相似度计算器
        
        Args:
            name: 算法名称，如果为None则返回默认算法
            
        Returns:
            相似度计算器实例
        """
        if name is None:
            # 返回第一个可用的计算器
            if self.similarity_calculators:
                return next(iter(self.similarity_calculators.values()))
            else:
                raise RuntimeError("没有可用的相似度计算算法")
        
        if name not in self.similarity_calculators:
            raise ValueError(f"相似度算法 '{name}' 不存在")
        
        return self.similarity_calculators[name]
    
    def get_available_detectors(self) -> List[str]:
        """获取可用的人脸检测算法列表"""
        return list(self.face_detectors.keys())
    
    def get_available_calculators(self) -> List[str]:
        """获取可用的相似度计算算法列表"""
        return list(self.similarity_calculators.keys())
    
    def get_detector_info(self, name: str) -> Dict[str, Any]:
        """获取人脸检测算法信息"""
        if name not in self.face_detectors:
            raise ValueError(f"人脸检测算法 '{name}' 不存在")
        
        return self.face_detectors[name].get_algorithm_info()
    
    def get_calculator_info(self, name: str) -> Dict[str, Any]:
        """获取相似度算法信息"""
        if name not in self.similarity_calculators:
            raise ValueError(f"相似度算法 '{name}' 不存在")
        
        return self.similarity_calculators[name].get_algorithm_info()
    
    def get_all_algorithms_info(self) -> Dict[str, Any]:
        """获取所有算法信息"""
        info = {
            'face_detectors': {},
            'similarity_calculators': {},
            'summary': {
                'total_detectors': len(self.face_detectors),
                'total_calculators': len(self.similarity_calculators)
            }
        }
        
        # 人脸检测算法信息
        for name, detector in self.face_detectors.items():
            info['face_detectors'][name] = detector.get_algorithm_info()
        
        # 相似度算法信息
        for name, calculator in self.similarity_calculators.items():
            info['similarity_calculators'][name] = calculator.get_algorithm_info()
        
        return info
    
    def set_detector_threshold(self, name: str, threshold: float):
        """设置人脸检测算法阈值"""
        if name not in self.face_detectors:
            raise ValueError(f"人脸检测算法 '{name}' 不存在")
        
        self.face_detectors[name].set_confidence_threshold(threshold)
        logger.info(f"人脸检测算法 {name} 阈值已设置为 {threshold}")
    
    def set_calculator_threshold(self, name: str, threshold: float):
        """设置相似度算法阈值"""
        if name not in self.similarity_calculators:
            raise ValueError(f"相似度算法 '{name}' 不存在")
        
        self.similarity_calculators[name].set_similarity_threshold(threshold)
        logger.info(f"相似度算法 {name} 阈值已设置为 {threshold}")
    
    def add_face_detector(self, detector):
        """添加新的人脸检测算法"""
        if detector.initialize():
            self.face_detectors[detector.name] = detector
            logger.info(f"新增人脸检测算法: {detector.name}")
        else:
            logger.error(f"人脸检测算法 {detector.name} 初始化失败")
    
    def add_similarity_calculator(self, calculator):
        """添加新的相似度计算算法"""
        if calculator.initialize():
            self.similarity_calculators[calculator.name] = calculator
            logger.info(f"新增相似度算法: {calculator.name}")
        else:
            logger.error(f"相似度算法 {calculator.name} 初始化失败")
    
    def remove_algorithm(self, name: str):
        """移除算法"""
        if name in self.face_detectors:
            del self.face_detectors[name]
            logger.info(f"已移除人脸检测算法: {name}")
        elif name in self.similarity_calculators:
            del self.similarity_calculators[name]
            logger.info(f"已移除相似度算法: {name}")
        else:
            logger.warning(f"算法 {name} 不存在")

# 全局算法管理器实例
algorithm_manager = AlgorithmManager()
