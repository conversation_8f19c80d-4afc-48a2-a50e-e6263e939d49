/* 文件上传组件样式 */

.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    background: var(--bg-primary);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: var(--primary-light);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: var(--primary-light);
    transform: scale(1.02);
}

.upload-area.uploading {
    pointer-events: none;
    opacity: 0.7;
}

.upload-icon {
    width: 48px;
    height: 48px;
    margin-bottom: var(--spacing-md);
    color: var(--text-muted);
    transition: color 0.3s;
}

.upload-area:hover .upload-icon,
.upload-area.dragover .upload-icon {
    color: var(--primary-color);
}

.upload-text {
    font-size: 1.125rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.upload-hint {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-lg);
}

.upload-button {
    margin-top: var(--spacing-md);
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

/* 文件预览 */
.file-preview {
    margin-top: var(--spacing-lg);
}

.file-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.file-item {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    background: var(--bg-primary);
    position: relative;
    transition: all 0.2s;
}

.file-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.file-thumbnail {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-sm);
    background: var(--bg-tertiary);
}

.file-info {
    text-align: left;
}

.file-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    word-break: break-word;
}

.file-size {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-sm);
}

.file-status {
    font-size: 0.75rem;
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    display: inline-block;
}

.file-status.uploading {
    background: var(--warning-color);
    color: white;
}

.file-status.success {
    background: var(--success-color);
    color: white;
}

.file-status.error {
    background: var(--error-color);
    color: white;
}

.file-actions {
    position: absolute;
    top: var(--spacing-xs);
    right: var(--spacing-xs);
    display: flex;
    gap: var(--spacing-xs);
}

.file-action-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: var(--radius-sm);
    background: rgba(0, 0, 0, 0.5);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
}

.file-action-btn:hover {
    background: rgba(0, 0, 0, 0.7);
}

.file-action-btn.delete {
    background: var(--error-color);
}

.file-action-btn.delete:hover {
    background: #dc2626;
}

/* 进度条 */
.upload-progress {
    margin-top: var(--spacing-md);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
    border-radius: var(--radius-sm);
}

.progress-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
    text-align: center;
}

/* 批量操作 */
.batch-actions {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.batch-info {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

/* 文件类型图标 */
.file-type-icon {
    width: 40px;
    height: 40px;
    margin: 0 auto var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    font-size: 1.25rem;
}

.file-type-icon.image {
    background: var(--primary-light);
    color: var(--primary-color);
}

.file-type-icon.unknown {
    background: var(--bg-tertiary);
    color: var(--text-muted);
}

/* 错误状态 */
.upload-error {
    color: var(--error-color);
    font-size: 0.875rem;
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: var(--radius-md);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.empty-state-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto var(--spacing-md);
    opacity: 0.5;
}

.empty-state-text {
    font-size: 1rem;
    margin-bottom: var(--spacing-sm);
}

.empty-state-hint {
    font-size: 0.875rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .upload-area {
        padding: var(--spacing-lg);
        min-height: 150px;
    }
    
    .file-list {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: var(--spacing-sm);
    }
    
    .file-thumbnail {
        height: 100px;
    }
    
    .batch-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .file-list {
        grid-template-columns: 1fr;
    }
    
    .upload-area {
        padding: var(--spacing-md);
    }
    
    .upload-text {
        font-size: 1rem;
    }
}

/* 动画效果 */
.file-item {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.file-removing {
    animation: slideOutDown 0.3s ease-in forwards;
}

@keyframes slideOutDown {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(20px);
    }
}

/* 拖拽指示器 */
.drag-indicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(37, 99, 235, 0.1);
    border: 3px dashed var(--primary-color);
    z-index: 1000;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.drag-indicator.active {
    display: flex;
}

/* 加载状态 */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: var(--spacing-xs);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
