// FaceMatchPro 主应用程序

class FaceMatchApp {
    constructor() {
        this.apiBase = '/api';
        this.sessionId = null;
        this.uploadedFiles = [];
        this.detectedFaces = [];
        this.selectedFaces = [];
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadSessionInfo();
        this.setupNotifications();
    }
    
    setupEventListeners() {
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeComponents();
        });
        
        // 全局错误处理
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            this.showNotification('发生了未知错误', 'error');
        });
        
        // 网络状态监听
        window.addEventListener('online', () => {
            this.showNotification('网络连接已恢复', 'success');
        });
        
        window.addEventListener('offline', () => {
            this.showNotification('网络连接已断开', 'warning');
        });
    }
    
    initializeComponents() {
        // 初始化上传组件
        if (document.querySelector('.upload-area')) {
            this.initUploadComponent();
        }

        // 初始化人脸检测组件
        if (document.querySelector('.detection-container')) {
            this.initDetectionComponent();
        }

        // 初始化比对组件
        if (document.querySelector('.comparison-container')) {
            this.initComparisonComponent();
        }

        // 初始化导航
        this.initNavigation();
    }

    // 上传组件初始化
    initUploadComponent() {
        const uploadArea = document.querySelector('.upload-area');
        const fileInput = document.querySelector('.file-input');
        const fileList = document.querySelector('.file-list');

        if (!uploadArea || !fileInput) return;

        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            if (!uploadArea.contains(e.relatedTarget)) {
                uploadArea.classList.remove('dragover');
            }
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = Array.from(e.dataTransfer.files);
            this.handleFileUpload(files);
        });

        // 点击上传
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            this.handleFileUpload(files);
        });

        // 加载已上传的文件
        this.loadUploadedFiles();
    }

    async handleFileUpload(files) {
        if (!files || files.length === 0) return;

        const uploadArea = document.querySelector('.upload-area');
        uploadArea.classList.add('uploading');

        try {
            for (const file of files) {
                await this.uploadSingleFile(file);
            }

            this.showNotification(`成功上传 ${files.length} 个文件`, 'success');
            this.loadUploadedFiles();
        } catch (error) {
            this.showNotification('文件上传失败: ' + error.message, 'error');
        } finally {
            uploadArea.classList.remove('uploading');
        }
    }

    async uploadSingleFile(file) {
        const formData = new FormData();
        formData.append('file', file);

        const response = await this.apiRequest('/upload/image', {
            method: 'POST',
            body: formData
        });

        if (!response.success) {
            throw new Error(response.message);
        }

        return response.data;
    }

    async loadUploadedFiles() {
        try {
            const response = await this.apiRequest('/upload/list');
            if (response.success) {
                this.uploadedFiles = response.data.files || [];
                this.renderFileList();
            }
        } catch (error) {
            console.error('Failed to load uploaded files:', error);
        }
    }

    renderFileList() {
        const fileList = document.querySelector('.file-list');
        if (!fileList) return;

        if (this.uploadedFiles.length === 0) {
            fileList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-text">暂无上传文件</div>
                    <div class="empty-state-hint">请先上传图像文件</div>
                </div>
            `;
            return;
        }

        fileList.innerHTML = this.uploadedFiles.map(file => `
            <div class="file-item" data-file-path="${file.file_path}">
                <div class="file-actions">
                    <button class="file-action-btn delete" onclick="app.deleteFile('${file.file_path}')" title="删除">
                        ×
                    </button>
                </div>
                ${file.is_image ?
                    `<img src="/static/uploads/${file.relative_path}" class="file-thumbnail" alt="${file.filename}">` :
                    `<div class="file-type-icon unknown">📄</div>`
                }
                <div class="file-info">
                    <div class="file-name" title="${file.filename}">${file.filename}</div>
                    <div class="file-size">${this.formatFileSize(file.size)}</div>
                    <div class="file-status success">已上传</div>
                </div>
            </div>
        `).join('');
    }

    async deleteFile(filePath) {
        if (!confirm('确定要删除这个文件吗？')) return;

        try {
            const response = await this.apiRequest('/upload/delete', {
                method: 'DELETE',
                body: JSON.stringify({ file_path: filePath })
            });

            if (response.success) {
                this.showNotification('文件删除成功', 'success');
                this.loadUploadedFiles();
            }
        } catch (error) {
            this.showNotification('文件删除失败: ' + error.message, 'error');
        }
    }
    
    initNavigation() {
        const navLinks = document.querySelectorAll('.navbar-nav a');
        const currentPath = window.location.pathname;
        
        navLinks.forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }
            
            link.addEventListener('click', (e) => {
                // 移除所有active类
                navLinks.forEach(l => l.classList.remove('active'));
                // 添加到当前链接
                e.target.classList.add('active');
            });
        });
    }
    
    // API 请求方法
    async apiRequest(endpoint, options = {}) {
        const url = `${this.apiBase}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin'
        };
        
        const config = { ...defaultOptions, ...options };
        
        // 如果是FormData，移除Content-Type让浏览器自动设置
        if (config.body instanceof FormData) {
            delete config.headers['Content-Type'];
        }
        
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || `HTTP ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }
    
    // 通知系统
    setupNotifications() {
        // 创建通知容器
        if (!document.querySelector('.notification-container')) {
            const container = document.createElement('div');
            container.className = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                max-width: 400px;
            `;
            document.body.appendChild(container);
        }
    }
    
    showNotification(message, type = 'info', duration = 5000) {
        const container = document.querySelector('.notification-container');
        const notification = document.createElement('div');
        
        const typeColors = {
            success: 'var(--success-color)',
            error: 'var(--error-color)',
            warning: 'var(--warning-color)',
            info: 'var(--primary-color)'
        };
        
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            background: ${typeColors[type]};
            color: white;
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            cursor: pointer;
        `;
        
        notification.textContent = message;
        container.appendChild(notification);
        
        // 动画显示
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 10);
        
        // 点击关闭
        notification.addEventListener('click', () => {
            this.removeNotification(notification);
        });
        
        // 自动关闭
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(notification);
            }, duration);
        }
    }
    
    removeNotification(notification) {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }
    
    // 会话管理
    async loadSessionInfo() {
        try {
            const response = await this.apiRequest('/upload/info');
            if (response.success) {
                this.sessionId = response.data.session_id;
                this.updateSessionDisplay();
            }
        } catch (error) {
            console.error('Failed to load session info:', error);
        }
    }
    
    updateSessionDisplay() {
        const sessionElements = document.querySelectorAll('.session-id');
        sessionElements.forEach(el => {
            el.textContent = this.sessionId || '未创建';
        });
    }
    
    // 工具方法
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
    
    formatTimestamp(timestamp) {
        return new Date(timestamp).toLocaleString('zh-CN');
    }
    
    generateId() {
        return Math.random().toString(36).substr(2, 9);
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // 加载状态管理
    showLoading(element, text = '加载中...') {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (!element) return;
        
        element.style.position = 'relative';
        
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        `;
        
        overlay.innerHTML = `
            <div style="text-align: center;">
                <div class="loading-spinner"></div>
                <div style="margin-top: 8px; color: var(--text-secondary);">${text}</div>
            </div>
        `;
        
        element.appendChild(overlay);
    }
    
    hideLoading(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (!element) return;
        
        const overlay = element.querySelector('.loading-overlay');
        if (overlay) {
            overlay.remove();
        }
    }
    
    // 模态框管理
    showModal(title, content, actions = []) {
        // 移除现有模态框
        const existingModal = document.querySelector('.modal-overlay');
        if (existingModal) {
            existingModal.remove();
        }
        
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        `;
        
        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';
        modalContent.style.cssText = `
            background: white;
            border-radius: 8px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        `;
        
        const header = document.createElement('div');
        header.style.cssText = `
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
        `;
        header.innerHTML = `<h3 style="margin: 0; font-size: 1.25rem;">${title}</h3>`;
        
        const body = document.createElement('div');
        body.style.cssText = `padding: 20px;`;
        body.innerHTML = content;
        
        const footer = document.createElement('div');
        footer.style.cssText = `
            padding: 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        `;
        
        actions.forEach(action => {
            const button = document.createElement('button');
            button.className = `btn ${action.class || 'btn-secondary'}`;
            button.textContent = action.text;
            button.onclick = () => {
                if (action.handler) {
                    action.handler();
                }
                if (action.close !== false) {
                    modal.remove();
                }
            };
            footer.appendChild(button);
        });
        
        modalContent.appendChild(header);
        modalContent.appendChild(body);
        modalContent.appendChild(footer);
        modal.appendChild(modalContent);
        
        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
        
        // ESC键关闭
        const escHandler = (e) => {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);
        
        document.body.appendChild(modal);
        
        return modal;
    }
}

// 全局实例
window.app = new FaceMatchApp();
