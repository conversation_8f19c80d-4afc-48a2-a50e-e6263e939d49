#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主视图模块
"""

from flask import render_template, session, current_app
from loguru import logger
from . import main_bp
from app.utils.session_manager import SessionManager

@main_bp.route('/')
def index():
    """首页"""
    try:
        # 初始化会话
        session_manager = SessionManager()
        session_id = session_manager.get_or_create_session()
        
        logger.info(f"用户访问首页，会话ID: {session_id}")
        
        return render_template('index.html', session_id=session_id)
    
    except Exception as e:
        logger.error(f"首页加载失败: {e}")
        return render_template('error.html', error="页面加载失败"), 500

@main_bp.route('/compare')
def compare():
    """人脸比对页面"""
    try:
        session_manager = SessionManager()
        session_id = session_manager.get_or_create_session()
        
        logger.info(f"用户访问比对页面，会话ID: {session_id}")
        
        return render_template('compare.html', session_id=session_id)
    
    except Exception as e:
        logger.error(f"比对页面加载失败: {e}")
        return render_template('error.html', error="页面加载失败"), 500

@main_bp.route('/results')
def results():
    """结果页面"""
    try:
        session_manager = SessionManager()
        session_id = session_manager.get_or_create_session()
        
        logger.info(f"用户访问结果页面，会话ID: {session_id}")
        
        return render_template('results.html', session_id=session_id)
    
    except Exception as e:
        logger.error(f"结果页面加载失败: {e}")
        return render_template('error.html', error="页面加载失败"), 500

@main_bp.route('/about')
def about():
    """关于页面"""
    try:
        return render_template('about.html')
    
    except Exception as e:
        logger.error(f"关于页面加载失败: {e}")
        return render_template('error.html', error="页面加载失败"), 500
