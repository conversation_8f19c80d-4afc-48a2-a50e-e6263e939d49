#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件工具类
"""

import os
import re
import time
import hashlib
from pathlib import Path
from typing import Set, Optional
from werkzeug.utils import secure_filename as werkzeug_secure_filename
from flask import current_app

class FileUtils:
    """文件工具类"""
    
    # 支持的图像格式
    IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif'}
    
    # 危险文件扩展名
    DANGEROUS_EXTENSIONS = {'.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js'}
    
    @staticmethod
    def is_allowed_file(filename: str) -> bool:
        """
        检查文件是否允许上传
        
        Args:
            filename: 文件名
            
        Returns:
            bool: 是否允许
        """
        if not filename:
            return False
        
        # 获取文件扩展名
        ext = Path(filename).suffix.lower()
        
        # 检查是否在允许的扩展名列表中
        allowed_extensions = current_app.config.get('ALLOWED_EXTENSIONS', FileUtils.IMAGE_EXTENSIONS)
        if isinstance(allowed_extensions, set):
            return ext in {f'.{e}' for e in allowed_extensions}
        else:
            return ext in FileUtils.IMAGE_EXTENSIONS
    
    @staticmethod
    def is_image_file(filename: str) -> bool:
        """
        检查是否为图像文件
        
        Args:
            filename: 文件名
            
        Returns:
            bool: 是否为图像文件
        """
        if not filename:
            return False
        
        ext = Path(filename).suffix.lower()
        return ext in FileUtils.IMAGE_EXTENSIONS
    
    @staticmethod
    def is_dangerous_file(filename: str) -> bool:
        """
        检查是否为危险文件
        
        Args:
            filename: 文件名
            
        Returns:
            bool: 是否为危险文件
        """
        if not filename:
            return False
        
        ext = Path(filename).suffix.lower()
        return ext in FileUtils.DANGEROUS_EXTENSIONS
    
    @staticmethod
    def secure_filename(filename: str) -> str:
        """
        生成安全的文件名
        
        Args:
            filename: 原始文件名
            
        Returns:
            str: 安全的文件名
        """
        if not filename:
            return 'unnamed_file'
        
        # 使用werkzeug的secure_filename
        safe_name = werkzeug_secure_filename(filename)
        
        # 如果结果为空，生成默认名称
        if not safe_name:
            ext = Path(filename).suffix.lower()
            safe_name = f'file{ext}' if ext else 'file'
        
        # 限制文件名长度
        name_part, ext_part = os.path.splitext(safe_name)
        if len(name_part) > 100:
            name_part = name_part[:100]
            safe_name = name_part + ext_part
        
        return safe_name
    
    @staticmethod
    def generate_unique_filename(directory: Path, filename: str) -> str:
        """
        生成唯一的文件名
        
        Args:
            directory: 目标目录
            filename: 原始文件名
            
        Returns:
            str: 唯一的文件名
        """
        base_name, ext = os.path.splitext(filename)
        counter = 1
        new_filename = filename
        
        while (directory / new_filename).exists():
            new_filename = f"{base_name}_{counter}{ext}"
            counter += 1
        
        return new_filename
    
    @staticmethod
    def get_file_hash(file_path: Path, algorithm: str = 'md5') -> str:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            algorithm: 哈希算法 ('md5', 'sha1', 'sha256')
            
        Returns:
            str: 哈希值
        """
        hash_func = hashlib.new(algorithm)
        
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_func.update(chunk)
            return hash_func.hexdigest()
        except Exception:
            return ""
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        格式化文件大小
        
        Args:
            size_bytes: 字节数
            
        Returns:
            str: 格式化的大小
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    @staticmethod
    def validate_file_size(file_size: int, max_size: Optional[int] = None) -> bool:
        """
        验证文件大小
        
        Args:
            file_size: 文件大小（字节）
            max_size: 最大允许大小（字节）
            
        Returns:
            bool: 是否符合要求
        """
        if max_size is None:
            max_size = current_app.config.get('MAX_CONTENT_LENGTH', 16 * 1024 * 1024)  # 16MB
        
        return file_size <= max_size
    
    @staticmethod
    def clean_filename(filename: str) -> str:
        """
        清理文件名，移除特殊字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            str: 清理后的文件名
        """
        if not filename:
            return 'unnamed'
        
        # 保留文件扩展名
        name, ext = os.path.splitext(filename)
        
        # 移除或替换特殊字符
        name = re.sub(r'[^\w\s-]', '', name)  # 只保留字母、数字、空格、连字符
        name = re.sub(r'[-\s]+', '-', name)   # 将多个连字符或空格替换为单个连字符
        name = name.strip('-')                # 移除首尾的连字符
        
        # 如果名称为空，使用默认名称
        if not name:
            name = 'file'
        
        return name + ext.lower()
    
    @staticmethod
    def get_mime_type(filename: str) -> str:
        """
        根据文件扩展名获取MIME类型
        
        Args:
            filename: 文件名
            
        Returns:
            str: MIME类型
        """
        ext = Path(filename).suffix.lower()
        
        mime_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.bmp': 'image/bmp',
            '.webp': 'image/webp',
            '.tiff': 'image/tiff',
            '.tif': 'image/tiff',
            '.pdf': 'application/pdf',
            '.txt': 'text/plain',
            '.json': 'application/json',
            '.xml': 'application/xml',
            '.zip': 'application/zip',
            '.rar': 'application/x-rar-compressed'
        }
        
        return mime_types.get(ext, 'application/octet-stream')
    
    @staticmethod
    def create_directory_structure(base_path: Path, subdirs: list) -> bool:
        """
        创建目录结构
        
        Args:
            base_path: 基础路径
            subdirs: 子目录列表
            
        Returns:
            bool: 是否成功
        """
        try:
            base_path.mkdir(parents=True, exist_ok=True)
            
            for subdir in subdirs:
                (base_path / subdir).mkdir(exist_ok=True)
            
            return True
        except Exception:
            return False
    
    @staticmethod
    def is_path_safe(path: str, base_path: str) -> bool:
        """
        检查路径是否安全（防止路径遍历攻击）
        
        Args:
            path: 要检查的路径
            base_path: 基础路径
            
        Returns:
            bool: 是否安全
        """
        try:
            # 解析路径
            resolved_path = Path(path).resolve()
            resolved_base = Path(base_path).resolve()
            
            # 检查是否在基础路径内
            return str(resolved_path).startswith(str(resolved_base))
        except Exception:
            return False
    
    @staticmethod
    def backup_file(file_path: Path, backup_dir: Optional[Path] = None) -> Optional[Path]:
        """
        备份文件
        
        Args:
            file_path: 要备份的文件路径
            backup_dir: 备份目录
            
        Returns:
            Optional[Path]: 备份文件路径
        """
        try:
            if not file_path.exists():
                return None
            
            if backup_dir is None:
                backup_dir = file_path.parent / 'backup'
            
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成备份文件名
            timestamp = int(time.time())
            backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
            backup_path = backup_dir / backup_name
            
            # 复制文件
            import shutil
            shutil.copy2(file_path, backup_path)
            
            return backup_path
        except Exception:
            return None
