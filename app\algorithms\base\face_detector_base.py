#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人脸检测算法基类
"""

from abc import ABC, abstractmethod
from typing import List, Tuple, Dict, Any, Optional
import numpy as np
from dataclasses import dataclass

@dataclass
class FaceBox:
    """人脸框数据类"""
    x: int
    y: int
    width: int
    height: int
    confidence: float
    landmarks: Optional[List[Tuple[int, int]]] = None
    
    @property
    def x2(self) -> int:
        """右下角x坐标"""
        return self.x + self.width
    
    @property
    def y2(self) -> int:
        """右下角y坐标"""
        return self.y + self.height
    
    @property
    def center(self) -> Tuple[int, int]:
        """中心点坐标"""
        return (self.x + self.width // 2, self.y + self.height // 2)
    
    @property
    def area(self) -> int:
        """面积"""
        return self.width * self.height
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'x': self.x,
            'y': self.y,
            'width': self.width,
            'height': self.height,
            'confidence': self.confidence,
            'landmarks': self.landmarks,
            'center': self.center,
            'area': self.area
        }

class FaceDetectorBase(ABC):
    """人脸检测算法基类"""
    
    def __init__(self, name: str, confidence_threshold: float = 0.5):
        """
        初始化人脸检测器
        
        Args:
            name: 算法名称
            confidence_threshold: 置信度阈值
        """
        self.name = name
        self.confidence_threshold = confidence_threshold
        self._is_initialized = False
    
    @abstractmethod
    def initialize(self) -> bool:
        """
        初始化算法模型
        
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    @abstractmethod
    def detect_faces(self, image: np.ndarray) -> List[FaceBox]:
        """
        检测图像中的人脸
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            List[FaceBox]: 检测到的人脸框列表
        """
        pass
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 预处理后的图像
        """
        # 默认不做处理，子类可以重写
        return image
    
    def postprocess_faces(self, faces: List[FaceBox]) -> List[FaceBox]:
        """
        人脸后处理
        
        Args:
            faces: 检测到的人脸列表
            
        Returns:
            List[FaceBox]: 后处理后的人脸列表
        """
        # 按置信度过滤
        filtered_faces = [
            face for face in faces 
            if face.confidence >= self.confidence_threshold
        ]
        
        # 按置信度排序
        filtered_faces.sort(key=lambda x: x.confidence, reverse=True)
        
        return filtered_faces
    
    def detect(self, image: np.ndarray) -> List[FaceBox]:
        """
        完整的人脸检测流程
        
        Args:
            image: 输入图像
            
        Returns:
            List[FaceBox]: 检测到的人脸框列表
        """
        if not self._is_initialized:
            if not self.initialize():
                raise RuntimeError(f"算法 {self.name} 初始化失败")
            self._is_initialized = True
        
        # 预处理
        processed_image = self.preprocess_image(image)
        
        # 检测人脸
        faces = self.detect_faces(processed_image)
        
        # 后处理
        final_faces = self.postprocess_faces(faces)
        
        return final_faces
    
    def get_algorithm_info(self) -> Dict[str, Any]:
        """
        获取算法信息
        
        Returns:
            Dict[str, Any]: 算法信息
        """
        return {
            'name': self.name,
            'confidence_threshold': self.confidence_threshold,
            'is_initialized': self._is_initialized,
            'type': 'face_detector'
        }
    
    def set_confidence_threshold(self, threshold: float):
        """设置置信度阈值"""
        if 0.0 <= threshold <= 1.0:
            self.confidence_threshold = threshold
        else:
            raise ValueError("置信度阈值必须在0.0到1.0之间")
    
    def __str__(self) -> str:
        return f"FaceDetector({self.name})"
    
    def __repr__(self) -> str:
        return f"FaceDetector(name='{self.name}', threshold={self.confidence_threshold})"
