{% extends "base.html" %}

{% block title %}文件上传 - FaceMatchPro{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- 页面标题 -->
    <div class="text-center mb-6">
        <h1 class="text-2xl font-bold mb-2">📁 文件上传</h1>
        <p class="text-secondary">上传图像文件进行人脸检测和比对分析</p>
    </div>
    
    <!-- 上传区域 -->
    <div class="card mb-6">
        <div class="card-header">
            <h2 class="card-title">上传图像文件</h2>
        </div>
        <div class="card-body">
            <div class="upload-area" id="upload-area">
                <input type="file" class="file-input" id="file-input" multiple accept="image/*">
                
                <div class="upload-icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                    </svg>
                </div>
                
                <div class="upload-text">拖拽文件到此处</div>
                <div class="upload-hint">或点击选择文件 (支持 JPG, PNG, BMP, TIFF 格式)</div>
                
                <button type="button" class="btn btn-primary upload-button">
                    选择文件
                </button>
            </div>
            
            <!-- 上传进度 -->
            <div class="upload-progress hidden" id="upload-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div class="progress-text" id="progress-text">上传中...</div>
            </div>
        </div>
    </div>
    
    <!-- 文件列表 -->
    <div class="card">
        <div class="card-header">
            <div class="flex justify-between items-center">
                <h2 class="card-title">已上传文件</h2>
                <div class="flex gap-2">
                    <button class="btn btn-sm btn-outline" onclick="app.loadUploadedFiles()">
                        🔄 刷新
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="clearAllFiles()">
                        🗑️ 清空
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- 批量操作 -->
            <div class="batch-actions hidden" id="batch-actions">
                <div class="batch-info">
                    已选择 <span id="selected-count">0</span> 个文件
                </div>
                <div class="flex gap-2">
                    <button class="btn btn-sm btn-primary" onclick="batchDetectFaces()">
                        👤 批量检测人脸
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="batchValidate()">
                        ✅ 批量验证
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="batchDelete()">
                        🗑️ 批量删除
                    </button>
                </div>
            </div>
            
            <!-- 文件预览列表 -->
            <div class="file-preview">
                <div class="file-list" id="file-list">
                    <!-- 文件项将通过JavaScript动态生成 -->
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                            </svg>
                        </div>
                        <div class="empty-state-text">暂无上传文件</div>
                        <div class="empty-state-hint">请先上传图像文件</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 上传统计 -->
    <div class="grid grid-cols-3 gap-4 mt-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="stat-value" id="total-files">0</div>
                <div class="stat-label">总文件数</div>
            </div>
        </div>
        
        <div class="card text-center">
            <div class="card-body">
                <div class="stat-value" id="total-size">0 B</div>
                <div class="stat-label">总大小</div>
            </div>
        </div>
        
        <div class="card text-center">
            <div class="card-body">
                <div class="stat-value" id="image-files">0</div>
                <div class="stat-label">图像文件</div>
            </div>
        </div>
    </div>
</div>

<!-- 文件详情模态框 -->
<div class="modal-overlay hidden" id="file-detail-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>文件详情</h3>
            <button class="btn btn-sm btn-outline" onclick="closeFileDetail()">×</button>
        </div>
        <div class="modal-body" id="file-detail-content">
            <!-- 详情内容将动态生成 -->
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeFileDetail()">关闭</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 文件选择状态
let selectedFiles = new Set();

// 扩展文件列表渲染
const originalRenderFileList = window.app.renderFileList;
window.app.renderFileList = function() {
    const fileList = document.querySelector('.file-list');
    if (!fileList) return;
    
    if (this.uploadedFiles.length === 0) {
        fileList.innerHTML = `
            <div class="empty-state">
                <div class="empty-state-icon">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                    </svg>
                </div>
                <div class="empty-state-text">暂无上传文件</div>
                <div class="empty-state-hint">请先上传图像文件</div>
            </div>
        `;
        updateStats();
        return;
    }
    
    fileList.innerHTML = this.uploadedFiles.map(file => `
        <div class="file-item ${selectedFiles.has(file.file_path) ? 'selected' : ''}" 
             data-file-path="${file.file_path}">
            <div class="file-actions">
                <button class="file-action-btn" onclick="toggleFileSelection('${file.file_path}')" title="选择">
                    ${selectedFiles.has(file.file_path) ? '✓' : '○'}
                </button>
                <button class="file-action-btn" onclick="showFileDetail('${file.file_path}')" title="详情">
                    ℹ️
                </button>
                <button class="file-action-btn delete" onclick="app.deleteFile('${file.file_path}')" title="删除">
                    ×
                </button>
            </div>
            ${file.is_image ? 
                `<img src="/static/uploads/${file.relative_path}" class="file-thumbnail" alt="${file.filename}" 
                     onclick="showImagePreview('${file.file_path}')">` :
                `<div class="file-type-icon unknown">📄</div>`
            }
            <div class="file-info">
                <div class="file-name" title="${file.filename}">${file.filename}</div>
                <div class="file-size">${this.formatFileSize(file.size)}</div>
                <div class="file-status success">已上传</div>
                <div class="file-meta">
                    <small class="text-muted">
                        ${this.formatTimestamp(file.upload_time)}
                    </small>
                </div>
            </div>
        </div>
    `).join('');
    
    updateStats();
    updateBatchActions();
};

// 文件选择切换
function toggleFileSelection(filePath) {
    if (selectedFiles.has(filePath)) {
        selectedFiles.delete(filePath);
    } else {
        selectedFiles.add(filePath);
    }
    
    // 更新UI
    const fileItem = document.querySelector(`[data-file-path="${filePath}"]`);
    if (fileItem) {
        fileItem.classList.toggle('selected');
        const selectBtn = fileItem.querySelector('.file-action-btn');
        selectBtn.textContent = selectedFiles.has(filePath) ? '✓' : '○';
    }
    
    updateBatchActions();
}

// 更新批量操作显示
function updateBatchActions() {
    const batchActions = document.getElementById('batch-actions');
    const selectedCount = document.getElementById('selected-count');
    
    if (selectedFiles.size > 0) {
        batchActions.classList.remove('hidden');
        selectedCount.textContent = selectedFiles.size;
    } else {
        batchActions.classList.add('hidden');
    }
}

// 更新统计信息
function updateStats() {
    const files = window.app.uploadedFiles || [];
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    const imageFiles = files.filter(file => file.is_image).length;
    
    document.getElementById('total-files').textContent = files.length;
    document.getElementById('total-size').textContent = window.app.formatFileSize(totalSize);
    document.getElementById('image-files').textContent = imageFiles;
}

// 显示文件详情
function showFileDetail(filePath) {
    const file = window.app.uploadedFiles.find(f => f.file_path === filePath);
    if (!file) return;
    
    const content = `
        <div class="file-detail">
            ${file.is_image ? 
                `<img src="/static/uploads/${file.relative_path}" style="max-width: 100%; height: auto; border-radius: 8px; margin-bottom: 16px;">` :
                ''
            }
            <div class="detail-grid" style="display: grid; grid-template-columns: 1fr 2fr; gap: 8px; font-size: 0.875rem;">
                <div><strong>文件名:</strong></div>
                <div>${file.filename}</div>
                
                <div><strong>文件大小:</strong></div>
                <div>${window.app.formatFileSize(file.size)}</div>
                
                <div><strong>文件类型:</strong></div>
                <div>${file.is_image ? '图像文件' : '其他文件'}</div>
                
                <div><strong>上传时间:</strong></div>
                <div>${window.app.formatTimestamp(file.upload_time)}</div>
                
                <div><strong>文件路径:</strong></div>
                <div style="word-break: break-all;">${file.file_path}</div>
                
                <div><strong>相对路径:</strong></div>
                <div style="word-break: break-all;">${file.relative_path}</div>
            </div>
        </div>
    `;
    
    document.getElementById('file-detail-content').innerHTML = content;
    document.getElementById('file-detail-modal').classList.remove('hidden');
}

// 关闭文件详情
function closeFileDetail() {
    document.getElementById('file-detail-modal').classList.add('hidden');
}

// 图像预览
function showImagePreview(filePath) {
    const file = window.app.uploadedFiles.find(f => f.file_path === filePath);
    if (!file || !file.is_image) return;
    
    const imageUrl = `/static/uploads/${file.relative_path}`;
    const content = `
        <div style="text-align: center;">
            <img src="${imageUrl}" style="max-width: 100%; max-height: 70vh; border-radius: 8px;">
            <div style="margin-top: 16px; font-size: 0.875rem; color: var(--text-secondary);">
                ${file.filename} (${window.app.formatFileSize(file.size)})
            </div>
        </div>
    `;
    
    window.app.showModal('图像预览', content, [
        { text: '关闭', class: 'btn-secondary' }
    ]);
}

// 批量操作
async function batchDetectFaces() {
    if (selectedFiles.size === 0) {
        window.app.showNotification('请先选择文件', 'warning');
        return;
    }
    
    const imagePaths = Array.from(selectedFiles).filter(path => {
        const file = window.app.uploadedFiles.find(f => f.file_path === path);
        return file && file.is_image;
    });
    
    if (imagePaths.length === 0) {
        window.app.showNotification('所选文件中没有图像文件', 'warning');
        return;
    }
    
    try {
        window.app.showLoading('.card-body', '批量检测人脸中...');
        
        const response = await window.app.apiRequest('/detection/batch', {
            method: 'POST',
            body: JSON.stringify({
                image_paths: imagePaths,
                detector: 'opencv_haar'  // 默认检测器
            })
        });
        
        if (response.success) {
            window.app.showNotification(
                `批量检测完成: ${response.data.success_count}/${response.data.total_images}`, 
                'success'
            );
            
            // 显示结果摘要
            const summary = `
                <div class="batch-summary">
                    <h4>批量检测结果</h4>
                    <div class="result-stats">
                        <div>总图像数: ${response.data.total_images}</div>
                        <div>成功检测: ${response.data.success_count}</div>
                        <div>检测到人脸: ${response.data.results.reduce((sum, r) => sum + (r.face_count || 0), 0)}</div>
                    </div>
                </div>
            `;
            
            window.app.showModal('批量检测结果', summary, [
                { text: '查看详情', class: 'btn-primary', handler: () => window.location.href = '/detection' },
                { text: '关闭', class: 'btn-secondary' }
            ]);
        }
    } catch (error) {
        window.app.showNotification('批量检测失败: ' + error.message, 'error');
    } finally {
        window.app.hideLoading('.card-body');
    }
}

async function batchValidate() {
    if (selectedFiles.size === 0) {
        window.app.showNotification('请先选择文件', 'warning');
        return;
    }
    
    try {
        const validationPromises = Array.from(selectedFiles).map(async (filePath) => {
            try {
                const response = await window.app.apiRequest('/upload/validate', {
                    method: 'POST',
                    body: JSON.stringify({ file_path: filePath })
                });
                return { filePath, success: response.success, message: response.message };
            } catch (error) {
                return { filePath, success: false, message: error.message };
            }
        });
        
        const results = await Promise.all(validationPromises);
        const successCount = results.filter(r => r.success).length;
        
        window.app.showNotification(
            `验证完成: ${successCount}/${results.length} 个文件有效`, 
            successCount === results.length ? 'success' : 'warning'
        );
        
        // 显示详细结果
        const content = `
            <div class="validation-results">
                ${results.map(result => `
                    <div class="validation-item ${result.success ? 'success' : 'error'}">
                        <div class="file-name">${result.filePath.split('/').pop()}</div>
                        <div class="validation-status">${result.success ? '✅ 有效' : '❌ 无效'}</div>
                        ${!result.success ? `<div class="error-message">${result.message}</div>` : ''}
                    </div>
                `).join('')}
            </div>
        `;
        
        window.app.showModal('验证结果', content, [
            { text: '关闭', class: 'btn-secondary' }
        ]);
        
    } catch (error) {
        window.app.showNotification('批量验证失败: ' + error.message, 'error');
    }
}

async function batchDelete() {
    if (selectedFiles.size === 0) {
        window.app.showNotification('请先选择文件', 'warning');
        return;
    }
    
    if (!confirm(`确定要删除选中的 ${selectedFiles.size} 个文件吗？`)) {
        return;
    }
    
    try {
        let successCount = 0;
        
        for (const filePath of selectedFiles) {
            try {
                const response = await window.app.apiRequest('/upload/delete', {
                    method: 'DELETE',
                    body: JSON.stringify({ file_path: filePath })
                });
                
                if (response.success) {
                    successCount++;
                }
            } catch (error) {
                console.error(`Failed to delete ${filePath}:`, error);
            }
        }
        
        window.app.showNotification(
            `删除完成: ${successCount}/${selectedFiles.size} 个文件`, 
            successCount === selectedFiles.size ? 'success' : 'warning'
        );
        
        // 清空选择并刷新列表
        selectedFiles.clear();
        window.app.loadUploadedFiles();
        
    } catch (error) {
        window.app.showNotification('批量删除失败: ' + error.message, 'error');
    }
}

async function clearAllFiles() {
    if (!window.app.uploadedFiles || window.app.uploadedFiles.length === 0) {
        window.app.showNotification('没有文件需要清空', 'info');
        return;
    }
    
    if (!confirm(`确定要删除所有 ${window.app.uploadedFiles.length} 个文件吗？`)) {
        return;
    }
    
    try {
        const response = await window.app.apiRequest('/upload/clear', {
            method: 'DELETE'
        });
        
        if (response.success) {
            window.app.showNotification('所有文件已清空', 'success');
            selectedFiles.clear();
            window.app.loadUploadedFiles();
        }
    } catch (error) {
        window.app.showNotification('清空文件失败: ' + error.message, 'error');
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 加载已上传的文件
    if (window.app && window.app.loadUploadedFiles) {
        window.app.loadUploadedFiles();
    }
});
</script>
{% endblock %}

{% block page_init %}
// 上传页面特定初始化
console.log('文件上传页面已加载');
{% endblock %}
