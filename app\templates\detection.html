{% extends "base.html" %}

{% block title %}人脸检测 - FaceMatchPro{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- 页面标题 -->
    <div class="text-center mb-6">
        <h1 class="text-2xl font-bold mb-2">👤 人脸检测</h1>
        <p class="text-secondary">使用先进算法检测和提取图像中的人脸</p>
    </div>
    
    <!-- 检测配置 -->
    <div class="grid grid-cols-2 gap-6 mb-6">
        <!-- 图像选择 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">选择图像</h2>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label class="form-label">选择要检测的图像:</label>
                    <select id="image-select" class="form-control form-select">
                        <option value="">请先上传图像文件...</option>
                    </select>
                </div>
                
                <div class="flex gap-2 mt-4">
                    <a href="{{ url_for('views.upload') }}" class="btn btn-outline btn-sm">
                        📁 上传图像
                    </a>
                    <button class="btn btn-secondary btn-sm" onclick="refreshImageList()">
                        🔄 刷新列表
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 算法配置 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">检测算法</h2>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label class="form-label">选择检测算法:</label>
                    <select id="detector-select" class="form-control form-select">
                        <option value="">加载中...</option>
                    </select>
                </div>
                
                <div class="flex gap-2 mt-4">
                    <button id="detect-btn" class="btn btn-primary">
                        🔍 开始检测
                    </button>
                    <button id="batch-detect-btn" class="btn btn-secondary">
                        📊 批量检测
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 算法信息 -->
    <div class="card mb-6">
        <div class="card-header">
            <h2 class="card-title">算法信息</h2>
        </div>
        <div class="card-body">
            <div class="algorithm-options" id="detector-info">
                <!-- 算法信息将动态加载 -->
                <div class="text-center text-muted">
                    <div class="loading-spinner"></div>
                    <div class="mt-2">加载算法信息中...</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 图像显示和检测结果 -->
    <div class="grid grid-cols-2 gap-6">
        <!-- 图像显示区域 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">图像预览</h2>
            </div>
            <div class="card-body">
                <div id="image-container" class="text-center">
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M9,3V4H4V6H5V19A2,2 0 0,0 7,21H17A2,2 0 0,0 19,19V6H20V4H15V3H9M7,6H17V19H7V6M9,8V17H11V8H9M13,8V17H15V8H13Z" />
                            </svg>
                        </div>
                        <div class="empty-state-text">请选择图像</div>
                        <div class="empty-state-hint">从上方下拉列表中选择要检测的图像</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 检测结果 -->
        <div class="card">
            <div class="card-header">
                <div class="flex justify-between items-center">
                    <h2 class="card-title">检测结果</h2>
                    <button id="extract-btn" class="btn btn-sm btn-success" disabled>
                        ✂️ 提取人脸
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="detection-results">
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M7.07,18.28C7.5,17.38 10.12,16.5 12,16.5C13.88,16.5 16.5,17.38 16.93,18.28C15.57,19.36 13.86,20 12,20C10.14,20 8.43,19.36 7.07,18.28M18.36,16.83C16.93,15.09 13.46,14.5 12,14.5C10.54,14.5 7.07,15.09 5.64,16.83C4.62,15.5 4,13.82 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,13.82 19.38,15.5 18.36,16.83M12,6C10.06,6 8.5,7.56 8.5,9.5C8.5,11.44 10.06,13 12,13C13.94,13 15.5,11.44 15.5,9.5C15.5,7.56 13.94,6 12,6M12,11A1.5,1.5 0 0,1 10.5,9.5A1.5,1.5 0 0,1 12,8A1.5,1.5 0 0,1 13.5,9.5A1.5,1.5 0 0,1 12,11Z" />
                            </svg>
                        </div>
                        <div class="empty-state-text">暂无检测结果</div>
                        <div class="empty-state-hint">请先选择图像并开始检测</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 检测历史 -->
    <div class="card mt-6">
        <div class="card-header">
            <div class="flex justify-between items-center">
                <h2 class="card-title">检测历史</h2>
                <div class="flex gap-2">
                    <button class="btn btn-sm btn-outline" onclick="loadDetectionHistory()">
                        🔄 刷新
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="clearDetectionHistory()">
                        🗑️ 清空历史
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div id="detection-history">
                <div class="empty-state">
                    <div class="empty-state-text">暂无检测历史</div>
                    <div class="empty-state-hint">完成人脸检测后将显示历史记录</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 快速操作 -->
    <div class="grid grid-cols-3 gap-4 mt-6">
        <div class="card text-center">
            <div class="card-body">
                <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">📊</div>
                <h4 class="font-semibold mb-2">批量检测</h4>
                <p class="text-sm text-secondary mb-4">对所有上传的图像进行批量人脸检测</p>
                <button class="btn btn-primary btn-sm" onclick="startBatchDetection()">
                    开始批量检测
                </button>
            </div>
        </div>
        
        <div class="card text-center">
            <div class="card-body">
                <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">⚙️</div>
                <h4 class="font-semibold mb-2">算法配置</h4>
                <p class="text-sm text-secondary mb-4">调整检测算法的参数和阈值</p>
                <button class="btn btn-secondary btn-sm" onclick="showAlgorithmConfig()">
                    配置算法
                </button>
            </div>
        </div>
        
        <div class="card text-center">
            <div class="card-body">
                <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">📈</div>
                <h4 class="font-semibold mb-2">检测统计</h4>
                <p class="text-sm text-secondary mb-4">查看检测性能和准确率统计</p>
                <button class="btn btn-outline btn-sm" onclick="showDetectionStats()">
                    查看统计
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 检测历史数据
let detectionHistory = [];

// 刷新图像列表
async function refreshImageList() {
    if (window.app && window.app.faceDetection) {
        await window.app.faceDetection.loadImages();
        window.app.showNotification('图像列表已刷新', 'success');
    }
}

// 开始批量检测
async function startBatchDetection() {
    if (window.app && window.app.faceDetection) {
        await window.app.faceDetection.batchDetect();
    }
}

// 显示算法配置
function showAlgorithmConfig() {
    const content = `
        <div class="algorithm-config">
            <h4>检测算法配置</h4>
            <div class="form-group">
                <label class="form-label">置信度阈值:</label>
                <input type="range" class="form-control" min="0.1" max="1.0" step="0.1" value="0.5" id="confidence-threshold">
                <div class="text-sm text-muted mt-1">当前值: <span id="threshold-value">0.5</span></div>
            </div>
            <div class="form-group">
                <label class="form-label">最小人脸尺寸:</label>
                <input type="number" class="form-control" min="20" max="200" value="30" id="min-face-size">
                <div class="text-sm text-muted mt-1">像素</div>
            </div>
            <div class="form-group">
                <label class="form-label">缩放因子:</label>
                <input type="number" class="form-control" min="1.1" max="2.0" step="0.1" value="1.1" id="scale-factor">
                <div class="text-sm text-muted mt-1">检测窗口缩放比例</div>
            </div>
        </div>
    `;
    
    const modal = window.app.showModal('算法配置', content, [
        { text: '保存配置', class: 'btn-primary', handler: saveAlgorithmConfig },
        { text: '取消', class: 'btn-secondary' }
    ]);
    
    // 添加阈值滑块事件
    const thresholdSlider = modal.querySelector('#confidence-threshold');
    const thresholdValue = modal.querySelector('#threshold-value');
    
    thresholdSlider.addEventListener('input', (e) => {
        thresholdValue.textContent = e.target.value;
    });
}

// 保存算法配置
async function saveAlgorithmConfig() {
    const threshold = document.getElementById('confidence-threshold').value;
    const minFaceSize = document.getElementById('min-face-size').value;
    const scaleFactor = document.getElementById('scale-factor').value;
    
    try {
        // 这里可以调用API保存配置
        window.app.showNotification('算法配置已保存', 'success');
        
        // 可以存储到localStorage
        localStorage.setItem('detectionConfig', JSON.stringify({
            threshold: parseFloat(threshold),
            minFaceSize: parseInt(minFaceSize),
            scaleFactor: parseFloat(scaleFactor)
        }));
        
    } catch (error) {
        window.app.showNotification('保存配置失败: ' + error.message, 'error');
    }
}

// 显示检测统计
function showDetectionStats() {
    const stats = {
        totalDetections: detectionHistory.length,
        totalFaces: detectionHistory.reduce((sum, item) => sum + (item.faceCount || 0), 0),
        averageFaces: detectionHistory.length > 0 ? 
            (detectionHistory.reduce((sum, item) => sum + (item.faceCount || 0), 0) / detectionHistory.length).toFixed(1) : 0,
        successRate: detectionHistory.length > 0 ? 
            ((detectionHistory.filter(item => item.success).length / detectionHistory.length) * 100).toFixed(1) : 0
    };
    
    const content = `
        <div class="detection-stats">
            <h4>检测统计信息</h4>
            <div class="stats-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-top: 16px;">
                <div class="stat-card" style="text-align: center; padding: 16px; border: 1px solid var(--border-color); border-radius: 8px;">
                    <div class="stat-value" style="font-size: 2rem; font-weight: bold; color: var(--primary-color);">${stats.totalDetections}</div>
                    <div class="stat-label" style="color: var(--text-secondary);">总检测次数</div>
                </div>
                <div class="stat-card" style="text-align: center; padding: 16px; border: 1px solid var(--border-color); border-radius: 8px;">
                    <div class="stat-value" style="font-size: 2rem; font-weight: bold; color: var(--success-color);">${stats.totalFaces}</div>
                    <div class="stat-label" style="color: var(--text-secondary);">检测到人脸</div>
                </div>
                <div class="stat-card" style="text-align: center; padding: 16px; border: 1px solid var(--border-color); border-radius: 8px;">
                    <div class="stat-value" style="font-size: 2rem; font-weight: bold; color: var(--warning-color);">${stats.averageFaces}</div>
                    <div class="stat-label" style="color: var(--text-secondary);">平均人脸数</div>
                </div>
                <div class="stat-card" style="text-align: center; padding: 16px; border: 1px solid var(--border-color); border-radius: 8px;">
                    <div class="stat-value" style="font-size: 2rem; font-weight: bold; color: var(--primary-color);">${stats.successRate}%</div>
                    <div class="stat-label" style="color: var(--text-secondary);">成功率</div>
                </div>
            </div>
        </div>
    `;
    
    window.app.showModal('检测统计', content, [
        { text: '关闭', class: 'btn-secondary' }
    ]);
}

// 加载检测历史
function loadDetectionHistory() {
    // 从localStorage加载历史记录
    const stored = localStorage.getItem('detectionHistory');
    if (stored) {
        detectionHistory = JSON.parse(stored);
    }
    
    renderDetectionHistory();
}

// 渲染检测历史
function renderDetectionHistory() {
    const historyContainer = document.getElementById('detection-history');
    if (!historyContainer) return;
    
    if (detectionHistory.length === 0) {
        historyContainer.innerHTML = `
            <div class="empty-state">
                <div class="empty-state-text">暂无检测历史</div>
                <div class="empty-state-hint">完成人脸检测后将显示历史记录</div>
            </div>
        `;
        return;
    }
    
    historyContainer.innerHTML = `
        <div class="history-list">
            ${detectionHistory.slice(-10).reverse().map((item, index) => `
                <div class="history-item" style="display: flex; justify-content: space-between; align-items: center; padding: 12px; border-bottom: 1px solid var(--border-color);">
                    <div class="history-info">
                        <div class="history-image">${item.imageName}</div>
                        <div class="history-meta" style="font-size: 0.875rem; color: var(--text-secondary);">
                            ${item.detector} • ${item.faceCount} 张人脸 • ${item.timestamp}
                        </div>
                    </div>
                    <div class="history-status">
                        <span class="status-badge ${item.success ? 'success' : 'error'}" style="padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; color: white; background: ${item.success ? 'var(--success-color)' : 'var(--error-color)'};">
                            ${item.success ? '成功' : '失败'}
                        </span>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

// 清空检测历史
function clearDetectionHistory() {
    if (!confirm('确定要清空所有检测历史吗？')) return;
    
    detectionHistory = [];
    localStorage.removeItem('detectionHistory');
    renderDetectionHistory();
    window.app.showNotification('检测历史已清空', 'success');
}

// 添加检测历史记录
function addDetectionHistory(imageName, detector, faceCount, success) {
    const historyItem = {
        imageName,
        detector,
        faceCount,
        success,
        timestamp: new Date().toLocaleString('zh-CN')
    };
    
    detectionHistory.push(historyItem);
    
    // 只保留最近100条记录
    if (detectionHistory.length > 100) {
        detectionHistory = detectionHistory.slice(-100);
    }
    
    // 保存到localStorage
    localStorage.setItem('detectionHistory', JSON.stringify(detectionHistory));
    
    renderDetectionHistory();
}

// 扩展检测组件以添加历史记录
if (window.app) {
    const originalDetectFaces = window.app.initDetectionComponent;
    window.app.initDetectionComponent = function() {
        originalDetectFaces.call(this);
        
        // 扩展检测方法以添加历史记录
        const originalDetect = this.faceDetection.detectFaces;
        this.faceDetection.detectFaces = async function() {
            const result = await originalDetect.call(this);
            
            // 添加到历史记录
            if (this.currentImage && this.selectedDetector) {
                const imageName = this.currentImage.split('/').pop();
                const faceCount = this.detectedFaces ? this.detectedFaces.length : 0;
                addDetectionHistory(imageName, this.selectedDetector, faceCount, true);
                
                // 启用提取按钮
                const extractBtn = document.getElementById('extract-btn');
                if (extractBtn && faceCount > 0) {
                    extractBtn.disabled = false;
                }
            }
            
            return result;
        };
    };
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    loadDetectionHistory();
});
</script>
{% endblock %}

{% block page_init %}
// 检测页面特定初始化
console.log('人脸检测页面已加载');
{% endblock %}
