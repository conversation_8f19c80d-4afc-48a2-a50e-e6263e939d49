#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件服务 - 处理文件上传、管理和清理
"""

import os
import shutil
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from flask import current_app
from loguru import logger

from app.utils.file_utils import FileUtils
from app.utils.session_manager import SessionManager

class FileService:
    """文件服务类"""
    
    def __init__(self):
        self.file_utils = FileUtils()
        self.session_manager = SessionManager()
    
    def save_uploaded_file(self, file, session_id: str, file_type: str = 'original') -> Dict[str, Any]:
        """
        保存上传的文件
        
        Args:
            file: Flask文件对象
            session_id: 会话ID
            file_type: 文件类型 ('original', 'processed', 'face')
            
        Returns:
            Dict: 保存结果
        """
        try:
            # 验证文件
            if not file or not file.filename:
                raise ValueError("无效的文件")
            
            # 检查文件扩展名
            if not self.file_utils.is_allowed_file(file.filename):
                raise ValueError("不支持的文件格式")
            
            # 生成安全的文件名
            filename = self.file_utils.secure_filename(file.filename)
            
            # 获取保存路径
            save_dir = self.session_manager.get_session_path(session_id, 'upload', file_type)
            save_dir.mkdir(parents=True, exist_ok=True)
            
            file_path = save_dir / filename
            
            # 如果文件已存在，添加时间戳
            if file_path.exists():
                name, ext = filename.rsplit('.', 1)
                timestamp = int(time.time())
                filename = f"{name}_{timestamp}.{ext}"
                file_path = save_dir / filename
            
            # 保存文件
            file.save(str(file_path))
            
            # 获取文件信息
            file_info = self.get_file_info(file_path)
            
            result = {
                'success': True,
                'file_path': str(file_path),
                'filename': filename,
                'file_type': file_type,
                'session_id': session_id,
                'file_info': file_info,
                'message': '文件保存成功'
            }
            
            logger.info(f"文件保存成功: {file_path}")
            return result
            
        except Exception as e:
            logger.error(f"文件保存失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '文件保存失败'
            }
    
    def get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 文件信息
        """
        try:
            stat = file_path.stat()
            
            info = {
                'size': stat.st_size,
                'size_human': self.file_utils.format_file_size(stat.st_size),
                'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'extension': file_path.suffix.lower(),
                'is_image': self.file_utils.is_image_file(file_path.name)
            }
            
            # 如果是图像文件，获取图像信息
            if info['is_image']:
                try:
                    import cv2
                    image = cv2.imread(str(file_path))
                    if image is not None:
                        info['image_info'] = {
                            'width': image.shape[1],
                            'height': image.shape[0],
                            'channels': image.shape[2] if len(image.shape) > 2 else 1
                        }
                except Exception as e:
                    logger.warning(f"获取图像信息失败: {e}")
            
            return info
            
        except Exception as e:
            logger.error(f"获取文件信息失败: {e}")
            return {}
    
    def list_session_files(self, session_id: str, file_type: str = None) -> Dict[str, Any]:
        """
        列出会话文件
        
        Args:
            session_id: 会话ID
            file_type: 文件类型过滤
            
        Returns:
            Dict: 文件列表
        """
        try:
            files = []
            
            # 获取会话目录
            session_path = self.session_manager.get_session_path(session_id, 'upload')
            
            if not session_path.exists():
                return {
                    'success': True,
                    'files': [],
                    'total_count': 0,
                    'message': '会话目录不存在'
                }
            
            # 遍历文件
            if file_type:
                # 指定类型的文件
                type_path = session_path / file_type
                if type_path.exists():
                    files.extend(self._scan_directory(type_path, file_type))
            else:
                # 所有类型的文件
                for subdir in session_path.iterdir():
                    if subdir.is_dir():
                        files.extend(self._scan_directory(subdir, subdir.name))
            
            # 按修改时间排序
            files.sort(key=lambda x: x['modified_time'], reverse=True)
            
            result = {
                'success': True,
                'session_id': session_id,
                'files': files,
                'total_count': len(files),
                'message': f'找到 {len(files)} 个文件'
            }
            
            return result
            
        except Exception as e:
            logger.error(f"列出会话文件失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '列出文件失败'
            }
    
    def _scan_directory(self, directory: Path, file_type: str) -> List[Dict[str, Any]]:
        """扫描目录中的文件"""
        files = []
        
        try:
            for file_path in directory.iterdir():
                if file_path.is_file():
                    file_info = self.get_file_info(file_path)
                    file_info.update({
                        'file_path': str(file_path),
                        'filename': file_path.name,
                        'file_type': file_type,
                        'relative_path': str(file_path.relative_to(directory.parent))
                    })
                    files.append(file_info)
        
        except Exception as e:
            logger.error(f"扫描目录失败 {directory}: {e}")
        
        return files
    
    def delete_file(self, file_path: str) -> Dict[str, Any]:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 删除结果
        """
        try:
            path = Path(file_path)
            
            if not path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            path.unlink()
            
            logger.info(f"文件已删除: {file_path}")
            return {
                'success': True,
                'file_path': file_path,
                'message': '文件删除成功'
            }
            
        except Exception as e:
            logger.error(f"文件删除失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '文件删除失败'
            }
    
    def cleanup_session_files(self, session_id: str) -> Dict[str, Any]:
        """
        清理会话文件
        
        Args:
            session_id: 会话ID
            
        Returns:
            Dict: 清理结果
        """
        try:
            deleted_count = 0
            
            # 清理上传目录
            upload_path = self.session_manager.get_session_path(session_id, 'upload')
            if upload_path.exists():
                shutil.rmtree(upload_path)
                deleted_count += 1
                logger.info(f"已清理上传目录: {upload_path}")
            
            # 清理临时目录
            temp_path = self.session_manager.get_session_path(session_id, 'temp')
            if temp_path.exists():
                shutil.rmtree(temp_path)
                deleted_count += 1
                logger.info(f"已清理临时目录: {temp_path}")
            
            return {
                'success': True,
                'session_id': session_id,
                'deleted_directories': deleted_count,
                'message': f'会话文件清理完成，删除 {deleted_count} 个目录'
            }
            
        except Exception as e:
            logger.error(f"清理会话文件失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '清理会话文件失败'
            }
    
    def cleanup_old_files(self, max_age_hours: int = 24) -> Dict[str, Any]:
        """
        清理过期文件
        
        Args:
            max_age_hours: 最大保留时间（小时）
            
        Returns:
            Dict: 清理结果
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            deleted_count = 0
            
            # 清理上传目录
            upload_base = Path(current_app.config['UPLOAD_FOLDER'])
            deleted_count += self._cleanup_old_directories(upload_base, cutoff_time)
            
            # 清理临时目录
            temp_base = Path(current_app.config['TEMP_FOLDER'])
            deleted_count += self._cleanup_old_directories(temp_base, cutoff_time)
            
            logger.info(f"过期文件清理完成，删除 {deleted_count} 个目录")
            
            return {
                'success': True,
                'max_age_hours': max_age_hours,
                'deleted_directories': deleted_count,
                'message': f'过期文件清理完成，删除 {deleted_count} 个目录'
            }
            
        except Exception as e:
            logger.error(f"清理过期文件失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '清理过期文件失败'
            }
    
    def _cleanup_old_directories(self, base_path: Path, cutoff_time: datetime) -> int:
        """清理过期目录"""
        deleted_count = 0
        
        try:
            if not base_path.exists():
                return 0
            
            for session_dir in base_path.iterdir():
                if session_dir.is_dir() and session_dir.name.startswith('session_'):
                    # 检查目录修改时间
                    dir_mtime = datetime.fromtimestamp(session_dir.stat().st_mtime)
                    
                    if dir_mtime < cutoff_time:
                        shutil.rmtree(session_dir)
                        deleted_count += 1
                        logger.debug(f"已删除过期目录: {session_dir}")
        
        except Exception as e:
            logger.error(f"清理目录失败 {base_path}: {e}")
        
        return deleted_count
    
    def get_storage_info(self) -> Dict[str, Any]:
        """获取存储信息"""
        try:
            upload_path = Path(current_app.config['UPLOAD_FOLDER'])
            temp_path = Path(current_app.config['TEMP_FOLDER'])
            
            info = {
                'upload_directory': {
                    'path': str(upload_path),
                    'exists': upload_path.exists(),
                    'size': self._get_directory_size(upload_path) if upload_path.exists() else 0
                },
                'temp_directory': {
                    'path': str(temp_path),
                    'exists': temp_path.exists(),
                    'size': self._get_directory_size(temp_path) if temp_path.exists() else 0
                }
            }
            
            # 格式化大小
            for dir_info in info.values():
                dir_info['size_human'] = self.file_utils.format_file_size(dir_info['size'])
            
            return info
            
        except Exception as e:
            logger.error(f"获取存储信息失败: {e}")
            return {}
    
    def _get_directory_size(self, directory: Path) -> int:
        """获取目录大小"""
        total_size = 0
        
        try:
            for file_path in directory.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception as e:
            logger.error(f"计算目录大小失败 {directory}: {e}")
        
        return total_size
