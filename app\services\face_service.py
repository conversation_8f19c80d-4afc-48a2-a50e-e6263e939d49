#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人脸服务 - 提供人脸检测和相似度比对的业务逻辑
"""

import cv2
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
from loguru import logger

from .algorithm_manager import algorithm_manager
from app.utils.image_utils import ImageUtils
from app.algorithms.base.face_detector_base import FaceBox
from app.algorithms.base.similarity_calculator_base import SimilarityResult

class FaceService:
    """人脸服务类"""
    
    def __init__(self):
        self.algorithm_manager = algorithm_manager
        self.image_utils = ImageUtils()
    
    def detect_faces_in_image(self, image_path: str, detector_name: str = None) -> Dict[str, Any]:
        """
        检测图像中的人脸
        
        Args:
            image_path: 图像文件路径
            detector_name: 人脸检测算法名称
            
        Returns:
            Dict: 检测结果
        """
        try:
            # 读取图像
            image = cv2.imread(str(image_path))
            if image is None:
                raise ValueError(f"无法读取图像: {image_path}")
            
            # 获取人脸检测器
            detector = self.algorithm_manager.get_face_detector(detector_name)
            
            # 检测人脸
            faces = detector.detect(image)
            
            # 构建结果
            result = {
                'success': True,
                'image_path': str(image_path),
                'image_size': {
                    'width': image.shape[1],
                    'height': image.shape[0]
                },
                'detector': detector.name,
                'face_count': len(faces),
                'faces': [face.to_dict() for face in faces],
                'message': f"检测到 {len(faces)} 张人脸"
            }
            
            logger.info(f"人脸检测完成: {image_path}, 检测到 {len(faces)} 张人脸")
            return result
            
        except Exception as e:
            logger.error(f"人脸检测失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '人脸检测失败'
            }
    
    def extract_face_regions(self, image_path: str, faces: List[Dict], output_dir: str) -> List[str]:
        """
        提取人脸区域并保存
        
        Args:
            image_path: 原始图像路径
            faces: 人脸框信息列表
            output_dir: 输出目录
            
        Returns:
            List[str]: 提取的人脸图像路径列表
        """
        try:
            # 读取原始图像
            image = cv2.imread(str(image_path))
            if image is None:
                raise ValueError(f"无法读取图像: {image_path}")
            
            face_paths = []
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            for i, face_info in enumerate(faces):
                # 提取人脸区域
                x = face_info['x']
                y = face_info['y']
                w = face_info['width']
                h = face_info['height']
                
                # 添加边距
                margin = 20
                x1 = max(0, x - margin)
                y1 = max(0, y - margin)
                x2 = min(image.shape[1], x + w + margin)
                y2 = min(image.shape[0], y + h + margin)
                
                face_image = image[y1:y2, x1:x2]
                
                # 保存人脸图像
                face_filename = f"face_{i+1}.jpg"
                face_path = output_path / face_filename
                cv2.imwrite(str(face_path), face_image)
                
                face_paths.append(str(face_path))
                logger.debug(f"人脸区域已保存: {face_path}")
            
            return face_paths
            
        except Exception as e:
            logger.error(f"人脸区域提取失败: {e}")
            return []
    
    def compare_faces(self, face1_path: str, face2_path: str, 
                     calculator_names: List[str] = None) -> Dict[str, Any]:
        """
        比较两张人脸图像的相似度
        
        Args:
            face1_path: 第一张人脸图像路径
            face2_path: 第二张人脸图像路径
            calculator_names: 相似度算法名称列表
            
        Returns:
            Dict: 比较结果
        """
        try:
            # 读取人脸图像
            face1 = cv2.imread(str(face1_path))
            face2 = cv2.imread(str(face2_path))
            
            if face1 is None or face2 is None:
                raise ValueError("无法读取人脸图像")
            
            # 获取要使用的算法
            if calculator_names is None:
                calculator_names = self.algorithm_manager.get_available_calculators()
            
            results = {}
            
            # 使用多个算法进行比较
            for calc_name in calculator_names:
                try:
                    calculator = self.algorithm_manager.get_similarity_calculator(calc_name)
                    similarity_result = calculator.compare_faces(face1, face2)
                    results[calc_name] = similarity_result.to_dict()
                    
                except Exception as e:
                    logger.error(f"算法 {calc_name} 比较失败: {e}")
                    results[calc_name] = {
                        'error': str(e),
                        'success': False
                    }
            
            # 计算平均相似度
            valid_results = [r for r in results.values() if 'similarity' in r]
            avg_similarity = 0.0
            if valid_results:
                avg_similarity = sum(r['similarity'] for r in valid_results) / len(valid_results)
            
            result = {
                'success': True,
                'face1_path': str(face1_path),
                'face2_path': str(face2_path),
                'results': results,
                'average_similarity': avg_similarity,
                'is_match': avg_similarity >= 0.6,  # 默认阈值
                'message': f"相似度: {avg_similarity:.3f}"
            }
            
            logger.info(f"人脸比较完成: {avg_similarity:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"人脸比较失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '人脸比较失败'
            }
    
    def batch_compare_faces(self, reference_face: str, target_faces: List[str],
                           calculator_name: str = None) -> Dict[str, Any]:
        """
        批量比较人脸
        
        Args:
            reference_face: 参考人脸图像路径
            target_faces: 目标人脸图像路径列表
            calculator_name: 相似度算法名称
            
        Returns:
            Dict: 批量比较结果
        """
        try:
            # 读取参考人脸
            ref_face = cv2.imread(str(reference_face))
            if ref_face is None:
                raise ValueError(f"无法读取参考人脸: {reference_face}")
            
            # 获取相似度计算器
            calculator = self.algorithm_manager.get_similarity_calculator(calculator_name)
            
            results = []
            
            for target_face in target_faces:
                try:
                    # 读取目标人脸
                    target_img = cv2.imread(str(target_face))
                    if target_img is None:
                        logger.warning(f"无法读取目标人脸: {target_face}")
                        continue
                    
                    # 计算相似度
                    similarity_result = calculator.compare_faces(ref_face, target_img)
                    
                    result_item = {
                        'target_path': str(target_face),
                        'similarity': similarity_result.similarity,
                        'distance': similarity_result.distance,
                        'confidence': similarity_result.confidence,
                        'is_match': similarity_result.is_match(calculator.similarity_threshold)
                    }
                    
                    results.append(result_item)
                    
                except Exception as e:
                    logger.error(f"比较失败 {target_face}: {e}")
                    results.append({
                        'target_path': str(target_face),
                        'error': str(e),
                        'success': False
                    })
            
            # 排序结果（按相似度降序）
            valid_results = [r for r in results if 'similarity' in r]
            valid_results.sort(key=lambda x: x['similarity'], reverse=True)
            
            result = {
                'success': True,
                'reference_face': str(reference_face),
                'calculator': calculator.name,
                'total_comparisons': len(target_faces),
                'successful_comparisons': len(valid_results),
                'results': valid_results,
                'best_match': valid_results[0] if valid_results else None,
                'message': f"批量比较完成，成功 {len(valid_results)}/{len(target_faces)} 个"
            }
            
            logger.info(f"批量人脸比较完成: {len(valid_results)}/{len(target_faces)}")
            return result
            
        except Exception as e:
            logger.error(f"批量人脸比较失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '批量人脸比较失败'
            }
    
    def get_face_analysis(self, image_path: str) -> Dict[str, Any]:
        """
        获取人脸分析信息
        
        Args:
            image_path: 图像路径
            
        Returns:
            Dict: 分析结果
        """
        try:
            # 检测人脸
            detection_result = self.detect_faces_in_image(image_path)
            
            if not detection_result['success']:
                return detection_result
            
            faces = detection_result['faces']
            
            # 分析每个人脸
            face_analysis = []
            
            for i, face in enumerate(faces):
                analysis = {
                    'face_id': i + 1,
                    'position': {
                        'x': face['x'],
                        'y': face['y'],
                        'width': face['width'],
                        'height': face['height']
                    },
                    'confidence': face['confidence'],
                    'area': face['area'],
                    'center': face['center'],
                    'aspect_ratio': face['width'] / face['height'],
                    'size_category': self._categorize_face_size(face['area'])
                }
                
                face_analysis.append(analysis)
            
            result = {
                'success': True,
                'image_path': str(image_path),
                'total_faces': len(faces),
                'face_analysis': face_analysis,
                'image_info': detection_result['image_size'],
                'message': f"分析完成，共 {len(faces)} 张人脸"
            }
            
            return result
            
        except Exception as e:
            logger.error(f"人脸分析失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '人脸分析失败'
            }
    
    def _categorize_face_size(self, area: int) -> str:
        """根据面积对人脸大小进行分类"""
        if area < 2500:  # 50x50
            return "小"
        elif area < 10000:  # 100x100
            return "中"
        else:
            return "大"
