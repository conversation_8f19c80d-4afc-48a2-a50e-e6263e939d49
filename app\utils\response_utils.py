#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
响应工具类
"""

from flask import jsonify
from typing import Any, Dict, Optional
from datetime import datetime

class ResponseUtils:
    """API响应工具类"""
    
    @staticmethod
    def success(message: str = "操作成功", data: Any = None, code: int = 200) -> tuple:
        """
        成功响应
        
        Args:
            message: 响应消息
            data: 响应数据
            code: HTTP状态码
            
        Returns:
            tuple: (响应JSON, 状态码)
        """
        response = {
            'success': True,
            'code': code,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'data': data
        }
        
        return jsonify(response), code
    
    @staticmethod
    def error(message: str = "操作失败", code: int = 400, data: Any = None, 
             error_code: str = None) -> tuple:
        """
        错误响应
        
        Args:
            message: 错误消息
            code: HTTP状态码
            data: 错误数据
            error_code: 错误代码
            
        Returns:
            tuple: (响应JSON, 状态码)
        """
        response = {
            'success': False,
            'code': code,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'data': data
        }
        
        if error_code:
            response['error_code'] = error_code
        
        return jsonify(response), code
    
    @staticmethod
    def paginated(data: list, page: int, per_page: int, total: int, 
                 message: str = "获取数据成功") -> tuple:
        """
        分页响应
        
        Args:
            data: 数据列表
            page: 当前页码
            per_page: 每页数量
            total: 总数量
            message: 响应消息
            
        Returns:
            tuple: (响应JSON, 状态码)
        """
        total_pages = (total + per_page - 1) // per_page
        
        pagination_info = {
            'page': page,
            'per_page': per_page,
            'total': total,
            'total_pages': total_pages,
            'has_prev': page > 1,
            'has_next': page < total_pages
        }
        
        response = {
            'success': True,
            'code': 200,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'data': data,
            'pagination': pagination_info
        }
        
        return jsonify(response), 200
    
    @staticmethod
    def validation_error(errors: Dict[str, list], message: str = "数据验证失败") -> tuple:
        """
        验证错误响应
        
        Args:
            errors: 验证错误字典
            message: 错误消息
            
        Returns:
            tuple: (响应JSON, 状态码)
        """
        response = {
            'success': False,
            'code': 422,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'errors': errors
        }
        
        return jsonify(response), 422
    
    @staticmethod
    def not_found(message: str = "资源不存在") -> tuple:
        """
        404错误响应
        
        Args:
            message: 错误消息
            
        Returns:
            tuple: (响应JSON, 状态码)
        """
        return ResponseUtils.error(message, 404)
    
    @staticmethod
    def unauthorized(message: str = "未授权访问") -> tuple:
        """
        401错误响应
        
        Args:
            message: 错误消息
            
        Returns:
            tuple: (响应JSON, 状态码)
        """
        return ResponseUtils.error(message, 401)
    
    @staticmethod
    def forbidden(message: str = "禁止访问") -> tuple:
        """
        403错误响应
        
        Args:
            message: 错误消息
            
        Returns:
            tuple: (响应JSON, 状态码)
        """
        return ResponseUtils.error(message, 403)
    
    @staticmethod
    def internal_error(message: str = "服务器内部错误") -> tuple:
        """
        500错误响应
        
        Args:
            message: 错误消息
            
        Returns:
            tuple: (响应JSON, 状态码)
        """
        return ResponseUtils.error(message, 500)
    
    @staticmethod
    def custom_response(success: bool, message: str, code: int, 
                       data: Any = None, **kwargs) -> tuple:
        """
        自定义响应
        
        Args:
            success: 是否成功
            message: 响应消息
            code: HTTP状态码
            data: 响应数据
            **kwargs: 其他参数
            
        Returns:
            tuple: (响应JSON, 状态码)
        """
        response = {
            'success': success,
            'code': code,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'data': data
        }
        
        # 添加其他参数
        response.update(kwargs)
        
        return jsonify(response), code
