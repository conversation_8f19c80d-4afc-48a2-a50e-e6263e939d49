# FaceMatchPro - 人脸相似度比对工具

基于Python+Flask的现代化Web界面人脸相似度比对工具，提供直观、易用的人脸识别和相似度分析功能。

## 项目特色

### 模块化设计
- 每种算法都封装在独立文件中
- 采用基类+实现类的设计模式
- 便于扩展和维护

### 多算法支持
- **人脸检测**: OpenCV、Dlib
- **相似度算法**: FaceNet、ArcFace、余弦相似度、欧几里得距离

### 完整的Web界面
- 拖拽上传功能
- 自动人脸检测和手动裁剪
- 多算法结果对比
- 实时结果展示

### 服务化架构
- 算法管理器统一管理所有算法
- 服务层封装业务逻辑
- RESTful API设计

### 用户友好
- 直观的可视化界面
- 实时进度反馈
- 历史记录管理

## 核心功能

- 人脸检测与识别
- 人脸区域自动裁剪
- 手动人脸区域选择
- 多算法人脸相似度比对
- 实时结果展示
- 批量处理功能
- 结果导出功能

## 技术架构

### 分层架构
- `api/` - 专门的API蓝图，RESTful接口
- `views/` - 页面视图蓝图，渲染HTML页面
- `services/` - 业务逻辑层
- `utils/` - 工具函数层，会话管理

### 文件系统存储设计
- 基于会话的文件组织结构
- 临时文件管理和清理机制
- 结果导出功能
- 不需要数据库

## 项目结构

```
FaceMatchPro/
├── app/
│   ├── __init__.py
│   ├── api/                    # API蓝图
│   │   ├── __init__.py
│   │   ├── face_detection.py
│   │   ├── face_comparison.py
│   │   └── file_management.py
│   ├── views/                  # 页面视图蓝图
│   │   ├── __init__.py
│   │   └── main.py
│   ├── services/               # 业务逻辑层
│   │   ├── __init__.py
│   │   ├── algorithm_manager.py
│   │   ├── face_service.py
│   │   └── file_service.py
│   ├── algorithms/             # 算法模块
│   │   ├── __init__.py
│   │   ├── base/
│   │   ├── detection/
│   │   └── similarity/
│   ├── utils/                  # 工具函数
│   │   ├── __init__.py
│   │   ├── session_manager.py
│   │   ├── file_utils.py
│   │   └── image_utils.py
│   ├── static/                 # 静态文件
│   │   ├── css/
│   │   ├── js/
│   │   └── images/
│   └── templates/              # HTML模板
│       ├── base.html
│       ├── index.html
│       └── results.html
├── uploads/                    # 上传文件目录
├── temp/                       # 临时文件目录
├── config/                     # 配置文件
│   ├── __init__.py
│   ├── development.py
│   ├── production.py
│   └── testing.py
├── tests/                      # 测试文件
├── docs/                       # 文档
├── requirements.txt            # Python依赖
├── run.py                      # 应用启动文件
└── README.md
```

## 快速开始

### 环境要求
- Python 3.8+
- pip

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行应用
```bash
python run.py
```

### 访问应用
打开浏览器访问: http://localhost:5000

## 开发指南

### 添加新算法
1. 在 `app/algorithms/` 对应目录下创建新的算法类
2. 继承相应的基类
3. 实现必要的方法
4. 在算法管理器中注册新算法

### API文档
详细的API文档请参考 `docs/api.md`

## 许可证
MIT License
